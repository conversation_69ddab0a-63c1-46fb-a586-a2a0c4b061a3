<template>
    <div>
        <div class="form-text p-15px w-100% bg-$color h-100%" v-for="item in modelValue" :key="item.id">
            <n-form label-placement="left" :show-feedback="false">
                <n-grid :cols="24" :x-gap="16" :y-gap="16">
                    <n-gi :span="12">
                        <n-form-item label="集团文件编号" label-width="130" style="margin: 8px">
                            <n-input v-model:value="item.fileNo" disabled />
                        </n-form-item>
                    </n-gi>
                    <n-gi :span="12">
                        <n-form-item label="集团文件名称" label-width="130" style="margin: 8px">
                            <n-input v-model:value="item.fileName" disabled />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="24" :x-gap="16" :y-gap="16">
                    <n-gi :span="12">
                        <n-form-item label="原文件编号" path="originalNumber" label-width="130" style="margin: 8px">
                            <n-input v-model:value="item.originalNumber" disabled />
                        </n-form-item>
                    </n-gi>
                    <n-gi :span="12">
                        <n-form-item label="原文件版次" path="originalVersion" label-width="130" style="margin: 8px">
                            <n-input v-model:value="item.originalVersion" disabled />
                        </n-form-item>
                    </n-gi>
                </n-grid>
            </n-form>
        </div>
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: ''
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
</script>
<style scoped lang="less"></style>

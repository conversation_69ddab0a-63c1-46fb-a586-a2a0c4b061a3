<template>
    <div class="transfer-container">
        <!-- 左侧组织树 -->
        <div class="transfer-panel">
            <div class="panel-header">
                <n-checkbox :checked="isAllLeftChecked" @update:checked="toggleLeftCheckAll">
                    {{ checkPersonnelLeft.length }}/{{ allPersonnelLeft.length }}
                </n-checkbox>
                <span class="panel-title">单位人员</span>
            </div>
            <n-input v-model:value="leftSearch" placeholder="请输入姓名/部门" clearable class="mb-8px" />
            <div class="panel-content">
                <n-tree
                    :data="processedTreeData"
                    :default-expand-all="leftSearch.length > 0"
                    :checked-keys="leftChecked"
                    :label-field="leftLabel"
                    :key-field="leftKey"
                    :render-label="renderLeftLabel"
                    cascade
                    checkable
                    block-line
                    expand-on-click
                    check-strategy="all"
                    @update:checked-keys="updateLeftChecked"
                />
            </div>
        </div>

        <!-- 中间操作按钮 -->
        <div class="transfer-action">
            <n-button size="small" type="primary" @click="handleGrant" :disabled="!checkPersonnelLeft.length">
                发放 →
            </n-button>
            <n-button size="small" type="warning" @click="handleRecycle" :disabled="!rightChecked.length">
                ← 回收
            </n-button>
        </div>

        <!-- 右侧已选人员列表（checkbox group） -->
        <div class="transfer-panel">
            <div class="panel-header">
                <n-checkbox :checked="isAllRightChecked" @update:checked="toggleRightCheckAll">
                    {{ rightChecked.length }}/{{ selectedList.length }}
                </n-checkbox>
                <span class="panel-title">已选人员</span>
            </div>
            <n-input v-model:value="rightSearch" placeholder="请输入姓名" clearable class="mb-8px" />
            <div class="panel-content">
                <n-checkbox-group v-model:value="rightChecked">
                    <div v-for="item in selectedList" :key="item[rightKey]" class="checkbox-item">
                        <n-checkbox :value="item[rightKey]" :label="item[rightLabel]" />
                    </div>
                </n-checkbox-group>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue';
import { NTree, NButton, NInput, NCheckbox, NCheckboxGroup, TreeOption } from 'naive-ui';

/**
 * 组件props说明：
 * treeData: 组织架构树（含人员）
 * selectedIds: 已选人员id数组
 * approvingIds: 审批中人员id数组
 * selectedList: 已选人员详细信息（右侧列表渲染用，需含key、label）
 */
const props = defineProps<{
    selectedIds?: string[];
    approvingIds?: string[];
    leftKeyField?: string;
    leftLabelField?: string;
    rightKeyField?: string;
    rightLabelField?: string;
    params?: any;
}>();

const emit = defineEmits<{
    (e: 'grant', persons: { key: string; label: string }[]): void;
    (e: 'recycle', keys: string[]): void;
}>();

// 自定义字段
const leftKey = computed(() => props.leftKeyField || 'key');
const leftLabel = computed(() => props.leftLabelField || 'label');
const rightKey = computed(() => props.rightKeyField || 'key');
const rightLabel = computed(() => props.rightLabelField || 'label');
const renderLeftLabel = ({ option }: { option: TreeOption }) => {
    const key = String(option[leftKey.value]);
    const baseLabel = option[leftLabel.value];
    let statusVNode: any = undefined;
    if (approvingIdsSet.value.has(key)) {
        statusVNode = h('span', { style: 'color: #f0a020; margin-left: 2px; font-size: 10px;' }, '（审批中）');
    } else if (selectedIdsSet.value.has(key)) {
        statusVNode = h('span', { style: 'color: #005EFF; margin-left: 2px; font-size: 10px;' }, '（已发放）');
    }
    return h('span', null, statusVNode ? [baseLabel, statusVNode] : [baseLabel]);
};

// 审批中和已选集合
const approvingIdsSet = computed(() => new Set(props.approvingIds || []));
const selectedIdsSet = computed(() => new Set(props.selectedIds || []));

/**
 * 获取所有节点 key
 * @param tree 组织树
 * @param type 'all' 返回全部节点key，'person' 仅返回没有children字段的人员key
 */
function getAllKeysLeft(tree: TreeOption[], type: 'all' | 'person' = 'all'): string[] {
    const keys: string[] = [];
    const dfs = (nodes: TreeOption[]) => {
        nodes.forEach((node) => {
            if (type === 'all') {
                keys.push(String(node[leftKey.value]));
            } else if (type === 'person' && !('children' in node)) {
                keys.push(String(node[leftKey.value]));
            }
            if (node.children && node.children.length) {
                dfs(node.children);
            }
        });
    };
    dfs(tree);
    return keys;
}

// 递归处理树，设置禁用
function markTreeDisabled(nodes: TreeOption[]): TreeOption[] {
    return nodes.map((node) => {
        const key = String(node[leftKey.value]);
        let disabled = false;
        if (selectedIdsSet.value.has(key) || approvingIdsSet.value.has(key)) {
            disabled = true;
        }
        const newNode: TreeOption = {
            ...node,
            disabled
        };
        if (node.children && node.children.length) {
            newNode.children = markTreeDisabled(node.children);
        }
        return newNode;
    });
}

// 组织树数据由内部获取
const treeData = ref<TreeOption[]>([]);
const processedTreeData = computed(() => markTreeDisabled(treeData.value));

/**
 * 左侧树相关
 */
const leftSearch = ref('');
const leftChecked = ref<string[]>([]);
const updateLeftChecked = (keys: string[]) => {
    leftChecked.value = keys;
};
const allPersonnelLeft = computed(() =>
    getAllKeysLeft(treeData.value, 'person').filter((k) => {
        // 过滤掉已发放、审批中和禁用的用户
        const node = findNodeByKey(treeData.value, k);
        return !selectedIdsSet.value.has(k) && !approvingIdsSet.value.has(k) && !node?.disabled;
    })
);
const checkPersonnelLeft = computed(() => {
    return leftChecked.value.filter((k) => allPersonnelLeft.value.includes(k));
});
const isAllLeftChecked = computed(() => {
    return (
        leftChecked.value.length === getAllKeysLeft(treeData.value).length && getAllKeysLeft(treeData.value).length > 0
    );
});
const toggleLeftCheckAll = (v: boolean) => {
    leftChecked.value = v ? getAllKeysLeft(treeData.value) : [];
};

/**
 * 右侧列表相关
 */
const rightSearch = ref('');
const rightChecked = ref<string[]>([]);
const selectedList = ref<any[]>([]);
const isAllRightChecked = computed(() => {
    return rightChecked.value.length === selectedList.value.length;
});
const toggleRightCheckAll = (v: boolean) => {
    rightChecked.value = v ? selectedList.value.map((item) => item[rightKey.value]) : [];
};

/**
 * 发放/回收 按钮
 */
function handleGrant() {
    // 找到左侧树新勾选的人员对象
    const allPersons: { key: string; label: string }[] = treeData.value
        .flatMap(function getPersons(node): { key: string; label: string }[] {
            if (!node.children) {
                return [
                    {
                        key: String(node[leftKey.value]),
                        label: String(node[leftLabel.value])
                    }
                ];
            }
            return node.children.flatMap(getPersons);
        })
        .filter((person) => {
            // 只包含在左侧选中且未禁用的用户
            const node = findNodeByKey(treeData.value, person.key);
            return checkPersonnelLeft.value.includes(person.key) && !node?.disabled;
        });

    // 只返回本次新选中的
    emit('grant', allPersons);
    leftChecked.value = [];
}
function handleRecycle() {
    emit('recycle', [...rightChecked.value]);
    rightChecked.value = [];
}

// 构建树形结构
function buildTreeData(orgData: any): any[] {
    const result: any[] = [];

    // 收集所有子节点
    const children: any[] = [];

    // 添加用户节点
    if (orgData.userInfo && Array.isArray(orgData.userInfo)) {
        orgData.userInfo.forEach((user: any) => {
            children.push({
                key: user.userId,
                label: user.nickname,
                value: user.userId,
                disabled: user.status === 2 // status 为 2 的用户设置为不可选中
            });
        });
    }

    // 处理子组织
    if (orgData.children && Array.isArray(orgData.children)) {
        orgData.children.forEach((child: any) => {
            const childOrgNode = buildTreeData(child)[0]; // 获取子组织的根节点
            children.push(childOrgNode);
        });
    }

    // 添加组织节点，只有在有子项时才添加 children 属性
    const orgNode: any = {
        key: orgData.orgId,
        label: orgData.orgName,
        value: orgData.orgId
    };

    // 只有在有子项时才添加 children 属性
    if (children.length > 0) {
        orgNode.children = children;
    }

    result.push(orgNode);
    return result;
}

// 根据 key 查找节点
function findNodeByKey(nodes: any[], key: string): any {
    for (const node of nodes) {
        if (node.key === key) {
            return node;
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeByKey(node.children, key);
            if (found) {
                return found;
            }
        }
    }
    return null;
}

const getUserTreeData = async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getUserList(props.params);

    if (res.data?.organizationUserInfo) {
        treeData.value = buildTreeData(res.data.organizationUserInfo);

        // 已选人员从 data.users 获取，可能是 null
        if (res.data.users && Array.isArray(res.data.users)) {
            selectedList.value = res.data.users.map((user: any) => ({
                key: user.userId,
                label: user.nickname,
                value: user.userId,
                disabled: user.status === 2
            }));
        } else {
            selectedList.value = [];
        }
    } else {
        treeData.value = [];
        selectedList.value = [];
    }
};

onMounted(() => {
    getUserTreeData();
});

// 暴露方法供父组件调用
defineExpose({
    getUserTreeData
});
</script>

<style scoped lang="less">
.transfer-container {
    display: flex;
    gap: 8px;
    height: 260px;
    .transfer-panel {
        width: 200px;
        border: 1px solid #e0e0e6;
        padding: 12px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .panel-title {
            font-weight: bold;
        }
        .panel-content {
            flex: 1;
            overflow-y: auto;
            &::-webkit-scrollbar {
                width: 2px;
            }
            &::-webkit-scrollbar-thumb {
                background-color: #ccc;
            }
        }
        .checkbox-item {
            margin-bottom: 4px;
        }
    }
    .transfer-action {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
    }
}
</style>

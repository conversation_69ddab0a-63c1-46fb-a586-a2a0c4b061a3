<template>
    <div class="recycle-form">
        <n-form class="useFormDialog" label-placement="left" :show-feedback="false" :label-width="110">
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="回收人" :span="12">
                    <n-input :value="modelValue.flowData?.recycler?.nickname || ''" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="回收日期" :span="12">
                    <n-input :value="modelValue.flowData?.recycler?.recycleDate || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-input
                        :value="modelValue.flowData?.distributeData?.distributeTypeText || ''"
                        readonly
                        placeholder=""
                    />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-input :value="modelValue.flowData?.distributeData?.fileTypeText || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input :value="modelValue.flowData?.distributeData?.fileCategory || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12">
                    <n-input :value="modelValue.flowData?.distributeData?.reason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" v-if="modelValue.flowData?.distributeData?.otherReason">
                    <n-input :value="modelValue.flowData?.distributeData?.otherReason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi
                    label="期望发放日期"
                    :span="12"
                    v-if="modelValue.flowData?.distributeData?.wishDistributeDateText"
                >
                    <n-input
                        :value="modelValue.flowData?.distributeData?.wishDistributeDateText || ''"
                        readonly
                        placeholder=""
                    />
                </n-form-item-gi>
                <n-form-item-gi label="回收原因" :span="12">
                    <n-input :value="modelValue.flowData?.recycleReason?.reason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" v-if="modelValue.flowData?.recycleReason?.otherReason">
                    <n-input :value="modelValue.flowData?.recycleReason?.otherReason || ''" readonly placeholder="" />
                </n-form-item-gi>
            </n-grid>

            <div class="mt-16px">
                <n-form-item label="回收清单" class="data-table"> </n-form-item>
                <vxe-table :data="tableData" border>
                    <vxe-column field="fileName" title="文件名称" width="160" />
                    <vxe-column field="fileNo" title="文件编号" width="140" />
                    <vxe-column field="fileForm" title="版本/版次" width="120" />
                    <vxe-column field="a" title="内发:电子文件-查阅" width="200" :formatter="aFormatter" />
                    <vxe-column field="b" title="内发:电子文件-查阅/下载" width="200" :formatter="bFormatter" />
                    <vxe-column field="c" title="内发:纸质文件-一次下载" width="200" :formatter="cFormatter" />
                </vxe-table>
            </div>
        </n-form>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { VxeTable, VxeColumn } from 'vxe-table';

const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: ''
    }
);
const { modelValue } = props;

function aFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((item: any) => item.label).join('，');
    }
    return '';
}

function bFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((item: any) => item.label).join('，');
    }
    return '';
}

function cFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((item: any) => item.label).join('，');
    }
    return '';
}

// 表格数据
const tableData = computed(() => {
    const flowData = props.modelValue?.flowData;
    const list = Array.isArray(flowData?.recycleList) ? flowData.recycleList : [];

    return list.map((file: any) => {
        // 将新的权限格式转换为表格需要的格式
        const a: Array<{ label: string; value: string; isRecycle?: boolean }> = [];
        const b: Array<{ label: string; value: string; isRecycle?: boolean }> = [];
        const c: Array<{ label: string; value: string; isRecycle?: boolean }> = [];

        if (file.permissions && Array.isArray(file.permissions)) {
            file.permissions.forEach((permission: any) => {
                if (permission.filePermission === 1) {
                    // 将用户名字字符串转换为对象数组
                    const userNames = permission.userNames ? permission.userNames.split('、') : [];
                    a.push(...userNames.map((name: string) => ({ label: name, value: name })));
                } else if (permission.filePermission === 2) {
                    const userNames = permission.userNames ? permission.userNames.split('、') : [];
                    b.push(...userNames.map((name: string) => ({ label: name, value: name })));
                } else if (permission.filePermission === 3) {
                    const userNames = permission.userNames ? permission.userNames.split('、') : [];
                    c.push(...userNames.map((name: string) => ({ label: name, value: name })));
                }
            });
        }

        return {
            fileName: file.fileName || '',
            fileNo: file.fileNumber || '',
            fileForm: file.fileVersion || '',
            a,
            b,
            c
        };
    });
});
console.log(props.modelValue);
</script>

<style scoped lang="less">
.recycle-form {
    width: 100%;
}
.data-table {
    :deep(.n-form-item-blank) {
        display: none;
    }
}
</style>

export default {
    create(data: BorrowingApplicationForm) {
        return request({
            url: '/nebula/api/v1/borrowing-application/create',
            method: 'post',
            data
        });
    },
    update(data: BorrowingApplicationForm) {
        return request({
            url: '/nebula/api/v1/borrowing-application/update',
            method: 'post',
            data
        });
    },
    list(data: BorrowingApplicationListParams) {
        return request({
            url: '/nebula/api/v1/borrowing-application/list',
            method: 'post',
            data
        });
    },
    delete(id: string) {
        return request({
            url: '/nebula/api/v1/borrowing-application/delete',
            method: 'post',
            data: { id }
        });
    }
};

export interface BorrowingApplicationListParams {
    approvalStatus?: string;
    documentCategoryId?: string;
    documentModule?: string;
    documentName?: string;
    documentNo?: string;
    noPage: boolean;
    page: number;
    pageSize: number;
    /**
     * 借阅人名称
     */
    userNickname?: string;
    [property: string]: any;
}

export interface BorrowingApplicationForm {
    /**
     * 借阅原因
     */
    borrowReason: string;
    /**
     * 借阅时间
     */
    borrowTime: number;
    /**
     * 文档列表
     */
    documents: Document[];
    /**
     * 归还时间
     */
    dueTime: number;
    [property: string]: any;
}

export interface Document {
    /**
     * 文档id
     */
    documentId: string;
    /**
     * 文档模块，1：书籍 ，2：内部文档 ， 3：外部文档
     */
    documentModuleType: string;
    /**
     * 文档版次号
     */
    documentVersionNo: string;
    [property: string]: any;
}

/**
 * 借阅清单列表字段
 */
export interface Datum {
    /**
     * 借阅状态
     */
    borrowStatus?: number;
    documentCategoryName?: string;
    documentId?: string;
    documentModuleName?: string;
    documentName?: string;
    documentNo?: string;
    /**
     * 文档有效性
     */
    documentValidity?: number;
    documentVersionNo?: string;
    [property: string]: any;
}

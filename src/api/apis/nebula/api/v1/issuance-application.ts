export default {
    // 获取发放申请列表
    getList(params: GetDetailParams) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/distribute/list',
            data: params
        });
    },
    // 新增发放申请获取用户列表
    getUserList(params: GetUserListParams) {
        return request({
            method: 'get',
            url: '/nebula/api/v1/document-library/permission/users',
            params
        });
    },
    // 暂存发放信息
    setDistributeInfo(params: SetDistributeInfoParams) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/temporary-storage/distribute-info',
            data: params
        });
    },
    // 获取发放申请详情
    getDetail(id: string) {
        return request({
            method: 'get',
            url: '/nebula/api/v1/document-library/distribute-info',
            params: { id }
        });
    },
    // 根据发放列表id获取清单信息
    getDistributeInventory(id: string) {
        return request({
            method: 'get',
            url: '/nebula/api/v1/document-library/distribute/inventory',
            params: { id }
        });
    },
    // 根据发放清单id查询记录
    getDistributeRecycleInfo(distributeListId: string) {
        return request({
            method: 'get',
            url: '/nebula/api/v1/document-library/distribute/recycle-info',
            params: { distributeListId }
        });
    }
};

export interface IssuanceApplicationRow {
    id: string;
    serialNumber?: number;
    issuanceType?: string;
    fileType?: string;
    category?: string;
    applyDate?: string;
    expectedIssueDate?: string;
    reviewer?: string;
    approver?: string;
    issueCount?: number;
    signCount?: number;
    disposalCount?: number;
    status?: number;
    applicant?: string;
    reason?: string;
    otherReason?: string;
    distributeList?: DistributeItem[];
    paperDisposalList?: any[];
}

export interface DistributeItem {
    fileId: string;
    fileName: string;
    fileNumber: string;
    fileVersion: number;
    permissions: Permission[];
}

export interface Permission {
    fileForm: number;
    filePermission: number;
    receivedBy: ReceivedBy[];
}

export interface ReceivedBy {
    userId: string;
    nickname: string;
    status: number; // 1已签收 | 2未签收 | 3已回收 | 4已处置
}

export interface GetDetailParams {
    page: number;
    pageSize: number;
    noPage: boolean;
    fileNumber?: string;
    fileName?: string;
    fileType?: number;
    distributeType?: number;
    fileCategory?: string[];
    status?: number;
    applicant?: string;
}
export interface GetUserListParams {
    fileId: string; //文件id
    fileForm: number; //文件所属，1内部文件 | 2外部文件
    filePermission: number; //文件权限1查阅 | 2查阅/下载 | 3一次下载
}
export interface SetDistributeInfoParams {
    saveMethod: number; //保存方式，1暂存
    applicant: string; //申请人
    applyDate: number; //申请日期
    distributeType: number | null; //发放类型
    fileType: number | null; //文件类型
    typeDictNodeId: string | null; //文件类别
    reason: string | null; //发放原因
    otherReason: string | null; //其他原因
    wishDistributeDate: number | null; //期望发放日期
    distributeList: any[]; //发放清单
}

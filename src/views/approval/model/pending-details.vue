<template>
    <alert-content :buttons="buttonConfig">
        <div class="todo-list-details flex gap-15px">
            <div class="left flex-v gap-15px w-calc-100%-320px">
                <div class="bg-#f8f9fc p-15px">
                    <div class="text-18px bold m-b-15px">{{ formTitle }}</div>
                    <form-validate
                        v-if="formFields && formFields.length"
                        class="useFormDialog"
                        v-model:model-value="formData"
                        :field="formFields"
                        :config="{
                            labelPlacement: 'left',
                            showFeedback: false,
                            requireMarkPlacement: 'left',
                            ...formConfig
                        }"
                        :grid-props="{
                            cols: 24,
                            yGap: '10px',
                            ...gridProps
                        }"
                    ></form-validate>
                    <component
                        v-else-if="formComponents"
                        :is="formComponents"
                        v-model:model-value="formData"
                        @error="handleComponentError"
                    />
                </div>
                <div v-if="type === 'handle'" class="bg-#f8f9fc p-15px">
                    <div class="text-18px bold m-b-15px">审批</div>
                    <n-form
                        ref="approveDataRef"
                        class="bg-#fff p-10px"
                        :model="approveData"
                        :rules="approveRules"
                        label-placement="left"
                        show-feedback
                    >
                        <n-form-item path="status">
                            <n-radio-group v-model:value="approveData.status">
                                <n-space>
                                    <n-radio v-for="item in approveOptions" :key="item.value" :value="item.value">
                                        {{ item.label }}
                                    </n-radio>
                                </n-space>
                            </n-radio-group>
                        </n-form-item>
                        <n-form-item path="comment">
                            <n-input
                                v-model:value="approveData.comment"
                                type="textarea"
                                placeholder="请输入审批意见"
                                :maxlength="500"
                                show-count
                            />
                        </n-form-item>
                        <minio-upload
                            v-model:file-list="approveData.extra.fileId"
                            :uploadProps="{
                                max: 1
                            }"
                        >
                            <n-button>上传附件</n-button>
                        </minio-upload>
                    </n-form>
                </div>
            </div>
            <div class="right w-300px bg-#f8f9fc p-15px">
                <bs-timeline :data="timelines"></bs-timeline>
            </div>
        </div>
    </alert-content>
</template>
<script setup lang="ts">
import { ApproveRequest } from '@/api/sass/api/v1/workflow';

const props = withDefaults(
    defineProps<{
        flowId: string;
        type: 'handle' | 'view' | 'recall';
        taskId?: string;
        isRead?: boolean;
        ccId?: string;
    }>(),
    {
        flowId: '',
        type: 'view',
        isRead: true
    }
);
const emit = defineEmits(['update:modelValue', 'close']);
const { flowId, type, taskId, isRead, ccId } = useVModels(props, emit);

// 审批单信息
const formTitle = ref('审批单');
const formData = ref({});
const formFields = ref<FormValidateField>([]);
const formComponents = shallowRef<any>(null);
const formConfig = ref<any>({});
const gridProps = ref<any>({});

// 处理组件错误
const handleComponentError = (error: Error) => {
    console.error('表单组件错误:', error);
    window.$message.error(`表单组件渲染失败: ${error.message}`);
};

// 初始化表单字段
const initFormFields = async () => {
    try {
        if (!flowDetail.value?.formContent) {
            throw new Error('审批数据不存在');
        }

        let parsedContent;
        try {
            parsedContent = JSON.parse(flowDetail.value.formContent);
            if (
                !parsedContent ||
                typeof parsedContent !== 'object' ||
                !parsedContent.businessId ||
                !parsedContent.version
            ) {
                throw new Error('解析审批数据失败');
            }
        } catch (e) {
            throw new Error('解析审批数据失败');
        }

        const config = await $datas.approvalAutoForm.getFormConfig(parsedContent.businessId, parsedContent.version);
        formTitle.value = config.title || '审批单';

        // 清除旧数据
        formFields.value = [];
        formComponents.value = null;

        // 设置新配置
        if (config.fields && Array.isArray(config.fields) && config.fields.length > 0) {
            formFields.value = config.fields;
        }

        if (config.components) {
            formComponents.value = config.components;
        }

        formConfig.value = config.formConfig || {};
        gridProps.value = config.gridProps || {};
        formData.value = parsedContent.data || {};
    } catch (error) {
        console.error('初始化表单失败:', error);
        window.$message.error(error instanceof Error ? error.message : (error as string) || '初始化表单失败');
        formFields.value = [];
        formComponents.value = null;
        formData.value = {};
    }
};

// 审批提交信息
const approveData = ref({
    status: '',
    comment: '',
    extra: {} as any
});
const approveRules = ref({
    status: [{ required: true, message: '请选择审批状态', trigger: 'change' }],
    comment: [{ required: true, message: '请输入审批意见', trigger: ['blur', 'input'] }]
});
const approveOptions = ref([
    { label: '同意', value: 'pass' },
    { label: '驳回', value: 'reject' }
]);

// 审批流程时间线
const timelines = ref([]);

// 按钮配置
const buttonConfig = computed(() => ({
    cancel: {
        text: type.value === 'handle' ? '取消' : '关闭',
        show: true
    },
    save: {
        text: '保存',
        show: type.value === 'handle',
        onClick: save
    }
}));

// 获取审批流程详情
const flowDetail = ref<any>({});
const getWorkflowDetail = async (id: string) => {
    const res = await api.sass.api.v1.workflow.workflow.detail(id);
    if (!res.data) {
        throw new Error('获取审批详情失败');
    }
    flowDetail.value = res.data;
    timelines.value = res.data.nodes;
};

// 提取文件信息的辅助函数
const getFileInfo = () => {
    const file = approveData.value.extra.fileId?.[0]?.response;
    if (!file) {
        return {};
    }

    return {
        fileId: file.id,
        fileName: file.name,
        format: file.name?.split('.')?.pop() || ''
    };
};

// 审批提交
const approveDataRef = ref();
const save = async () => {
    await approveDataRef.value?.validate();

    const extraData = await getFileInfo();

    const data: ApproveRequest = {
        taskId: taskId.value as string,
        ...approveData.value,
        extra: extraData
    };
    await api.sass.api.v1.workflow.workflow.approve(data);
    window.$message.success('审批成功');
};

onMounted(async () => {
    await getWorkflowDetail(flowId.value);
    await initFormFields();
    if (!isRead.value && ccId.value) {
        await api.sass.api.v1.workflow.workflow.ccRead(ccId.value);
        window.$message.success('审批流程已阅');
    }
});
</script>

<style scoped lang="less"></style>

<template>
    <div class="distribution-list-vxe-table">
        <vxe-table
            ref="tableRef"
            :data="flatTableData"
            :border="true"
            :span-method="spanMethod"
            auto-resize
            :edit-rules="validRules"
            :valid-config="{ showMessage: false }"
            :edit-config="{ trigger: 'click', mode: 'cell', showStatus: false, enabled: canEditTable }"
            @edit-closed="handleEditClosed"
            :tooltip-config="tooltipConfig"
        >
            <vxe-column field="fileName" title="文件名称" width="160" :edit-render="{}">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileName"
                        :options="getFileNameOptions()"
                        filterable
                        clearable
                        @update:value="(val) => onFileNameSelect(row, val)"
                        placeholder="选择文件名称"
                        style="width: 100%"
                    />
                </template>
            </vxe-column>
            <vxe-column field="number" title="文件编号" width="140" :edit-render="{}">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.number"
                        :options="getFileNoOptions()"
                        filterable
                        clearable
                        @update:value="(val) => onFileNoSelect(row, val)"
                        placeholder="选择文件编号"
                        style="width: 100%"
                    />
                </template>
            </vxe-column>
            <vxe-column field="version" title="版本/版次" width="120" :edit-render="{}">
                <template #edit="{ row }">
                    <n-input
                        v-model:value="row.version"
                        size="small"
                        placeholder="请输入版本/版次"
                        readonly
                        disabled
                    ></n-input>
                </template>
            </vxe-column>
            <vxe-column
                field="fileForm"
                title="文件形式"
                min-width="140"
                :edit-render="{}"
                :formatter="fileFormFormatter"
            >
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileForm"
                        size="small"
                        clearable
                        :options="getFileFormOptions(row)"
                        placeholder="请选择文件形式"
                        :disabled="getFileFormOptions(row).length === 0"
                    ></n-select>
                </template>
            </vxe-column>
            <vxe-column
                field="filePermission"
                title="文件权限"
                min-width="120"
                :edit-render="{}"
                :formatter="filePermissionFormatter"
            >
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.filePermission"
                        size="small"
                        :options="getFilePermissionOptions(row)"
                        placeholder="请选择文件权限"
                        :disabled="getFilePermissionOptions(row).length === 0"
                    ></n-select>
                </template>
            </vxe-column>
            <vxe-column field="receiver" title="接收方" width="120" :edit-render="{}">
                <template #edit="{ row }">
                    <n-input
                        v-model:value="row.receiver"
                        size="small"
                        placeholder="请输入接收方"
                        :disabled="!shouldShowReceiver(row)"
                    ></n-input>
                </template>
            </vxe-column>
            <vxe-column field="receivedBy" title="接收人" width="180" :formatter="() => ''" show-overflow="tooltip">
                <template #default="{ row }">
                    <n-button size="small" @click="openPersonModal(row)"> 选择接收人 </n-button>
                </template>
            </vxe-column>
            <vxe-column field="actions" title="操作" width="145">
                <template #default="{ row }">
                    <n-space :size="5" justify="center">
                        <n-button
                            v-if="row.permIndex === itemList[row.fileIndex].permissions.length - 1"
                            type="primary"
                            size="tiny"
                            @click="addPermission(row.fileIndex)"
                        >
                            增加权限
                        </n-button>
                        <n-button
                            v-if="itemList[row.fileIndex].permissions.length > 1"
                            type="warning"
                            size="tiny"
                            @click="removePermission(row.fileIndex, row.permIndex)"
                        >
                            删除权限
                        </n-button>
                        <n-button
                            v-if="row.permIndex === 0"
                            type="error"
                            size="tiny"
                            @click="removeFile(row.fileIndex)"
                        >
                            删除文件
                        </n-button>
                    </n-space>
                </template>
            </vxe-column>
        </vxe-table>

        <n-modal v-model:show="showPersonModal" preset="card" title="选择接收人" :style="{ width: '800px' }">
            <div class="transfer-container">
                <div class="transfer-panel">
                    <div class="panel-header">
                        <n-checkbox :checked="isAllLeftChecked" @update:checked="toggleLeftCheckAll">
                            {{ checkPersonnelLeft.length }}/{{ allPersonnelLeft.length }}
                        </n-checkbox>
                        <span class="panel-title">单位人员</span>
                    </div>
                    <n-input v-model:value="leftSearch" placeholder="请输入姓名/部门" clearable class="mb-8px" />
                    <div class="panel-content">
                        <n-tree
                            :data="processedTreeData"
                            :default-expand-all="leftSearch.length > 0"
                            :checked-keys="leftChecked"
                            cascade
                            checkable
                            block-line
                            expand-on-click
                            check-strategy="all"
                            :show-line="false"
                            :indent="16"
                            :show-irrelevant-nodes="false"
                            :pattern="leftSearch"
                            @update:checked-keys="updateLeftChecked"
                        />
                    </div>
                </div>
                <div class="transfer-action">
                    <n-button size="small" type="primary" @click="handleAdd" :disabled="!checkPersonnelLeft.length">
                        添加 →
                    </n-button>
                    <n-button size="small" type="warning" @click="handleRemove" :disabled="!rightChecked.length">
                        ← 移除
                    </n-button>
                </div>
                <div class="transfer-panel">
                    <div class="panel-header">
                        <n-checkbox :checked="isAllRightChecked" @update:checked="toggleRightCheckAll">
                            {{ rightChecked.length }}/{{ selectedList.length }}
                        </n-checkbox>
                        <span class="panel-title">已选人员</span>
                    </div>
                    <n-input v-model:value="rightSearch" placeholder="请输入姓名" clearable class="mb-8px" />
                    <div class="panel-content">
                        <n-checkbox-group v-model:value="rightChecked">
                            <div v-for="item in filteredSelectedList" :key="item.key" class="checkbox-item">
                                <n-checkbox :value="item.key" :label="item.label" />
                            </div>
                        </n-checkbox-group>
                    </div>
                </div>
            </div>
            <template #footer>
                <n-space justify="center">
                    <n-button @click="showPersonModal = false">取消</n-button>
                    <n-button type="primary" @click="confirmPersonSelection">确定</n-button>
                </n-space>
            </template>
        </n-modal>
    </div>
</template>

<script setup lang="ts">
import { RowVO } from '@/api/sass/api/v1/dict';
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';
import { NTree, NButton, NInput, NCheckbox, NCheckboxGroup } from 'naive-ui';
import useStore from '@/store/modules/main';

interface PersonItem {
    userId: string;
    userName: string;
}
interface PermissionItem {
    fileForm: string | null;
    filePermission: string | null;
    receiver: string;
    receivedBy: PersonItem[];
}
interface IssuanceItem {
    fileId: string;
    fileName: string;
    number: string;
    version: string;
    permissions: PermissionItem[];
}

const props = defineProps<{
    modelValue: IssuanceItem[];
    issuanceType?: number | null;
    fileType?: number | null;
    fileCategory?: string | null;
}>();
const emit = defineEmits(['update:modelValue']);

// 文件形式
const fileFormOptions = [
    { label: '电子文件', value: 1 },
    { label: '纸质文件', value: 2 }
];

const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    fileName: [{ required: true, message: '请输入文件名称' }],
    number: [{ required: true, message: '请输入文件编号' }],
    version: [{ required: true, message: '请输入版本/版次' }],
    fileForm: [{ required: true, message: '请选择文件形式' }],
    filePermission: [{ required: true, message: '请选择文件权限' }],
    receivedBy: [
        {
            required: true,
            validator({ cellValue }) {
                if (!Array.isArray(cellValue) || cellValue.length === 0) {
                    return new Error('请选择接收人');
                }
            }
        }
    ]
});

const itemList = ref<IssuanceItem[]>(props.modelValue || []);
watch(
    () => props.modelValue,
    (newValue) => {
        itemList.value = [...newValue];
    },
    { immediate: true, deep: true }
);

watch(
    [() => props.issuanceType, () => props.fileType, () => props.fileCategory],
    ([newIssuanceType, newFileType, newFileCategory], [oldIssuanceType, oldFileType, oldFileCategory]) => {
        if (
            (newIssuanceType !== oldIssuanceType ||
                newFileType !== oldFileType ||
                newFileCategory !== oldFileCategory) &&
            itemList.value.length === 0
        ) {
            itemList.value = [];
            update();
        }
    }
);

function update() {
    emit('update:modelValue', itemList.value);
}

function handleEditClosed({ row, column }: { row: any; column: { field: string } }) {
    console.log(first)
    const { field } = column;
    const { fileIndex, permIndex } = row;

    if (['fileName', 'number', 'version'].includes(field)) {
        (itemList.value[fileIndex] as unknown as { [key: string]: any })[field] = row[field];
    } else if (['fileForm', 'filePermission', 'receiver', 'receivedBy'].includes(field)) {
        if (field === 'receivedBy') {
            (itemList.value[fileIndex].permissions[permIndex] as { [key: string]: any })[field] =
                Array.isArray(row[field]) && row[field].length > 0
                    ? row[field].map((p: any) => ({
                          userId: p.userId,
                          userName: p.userName
                      }))
                    : [];
        } else if (field === 'fileForm' || field === 'filePermission') {
            // 只有当值不为空且有效时才进行转换和赋值
            if (row[field] !== null && row[field] !== undefined && row[field] !== '') {
                (itemList.value[fileIndex].permissions[permIndex] as { [key: string]: any })[field] = Number(row[field]);
            }
            // 如果值为空，保持原有值不变，不进行赋值操作
        } else {
            (itemList.value[fileIndex].permissions[permIndex] as { [key: string]: any })[field] = row[field];
        }
    }

    update();
}

function addFile() {
    itemList.value.push({
        fileId: '',
        fileName: '',
        number: '',
        version: '',
        permissions: [
            {
                fileForm: null,
                filePermission: null,
                receiver: '',
                receivedBy: []
            }
        ]
    });
    update();
}
function removeFile(fileIndex: number) {
    itemList.value.splice(fileIndex, 1);
    update();
}
function addPermission(fileIndex: number) {
    itemList.value[fileIndex].permissions.push({
        fileForm: '',
        filePermission: '',
        receiver: '',
        receivedBy: []
    });
    update();
}
function removePermission(fileIndex: number, permIndex: number) {
    itemList.value[fileIndex].permissions.splice(permIndex, 1);
    if (itemList.value[fileIndex].permissions.length === 0) {
        removeFile(fileIndex);
    } else {
        update();
    }
}
const flatTableData = computed(() => {
    const arr: any[] = [];
    itemList.value.forEach((file, fileIndex) => {
        file.permissions.forEach((perm, permIndex) => {
            const processedReceivedBy =
                Array.isArray(perm.receivedBy) && perm.receivedBy.length > 0 && typeof perm.receivedBy[0] === 'object'
                    ? perm.receivedBy.map((p: any) => ({
                          ...p,
                          name: p.name || p.userName || p.userNickname || p.id
                      }))
                    : (perm.receivedBy || []).map((p: any) => ({ id: p, name: p }));

            arr.push({
                fileIndex,
                permIndex,
                fileId: file.fileId,
                fileName: file.fileName,
                number: file.number,
                version: file.version,
                fileForm: perm.fileForm || null,
                filePermission: perm.filePermission || null,
                receiver: perm.receiver,
                receivedBy: processedReceivedBy,
                issuanceType: props.issuanceType || ''
            });
        });
    });
    return arr;
});

function spanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
    const row = flatTableData.value[rowIndex];
    const file = itemList.value[row.fileIndex];
    const permIndex = row.permIndex;
    const permCount = file.permissions.length;
    if ([0, 1, 2].includes(columnIndex)) {
        if (permIndex === 0) {
            return { rowspan: permCount, colspan: 1 };
        } else {
            return { rowspan: 0, colspan: 0 };
        }
    }
    return { rowspan: 1, colspan: 1 };
}

const tableRef = ref();
const tableValid = async () => {
    const $table = tableRef.value;
    if ($table) {
        const errMap = await $table.validate(true);
        if (errMap) {
            const firstKey = Object.keys(errMap)[0];
            if (firstKey && errMap[firstKey] && errMap[firstKey].length > 0) {
                const firstError = errMap[firstKey][0];
                const errorMessage =
                    firstError.rule?.$options?.message || firstError.rule?.$options?.content || '请检查表格数据';
                throw new Error(errorMessage);
            }
        }
        return null;
    }
    return null;
};

function shouldShowReceiver(row: any) {
    return row.issuanceType === 2 && row.fileForm === 1 && row.filePermission === 3;
}

const canEditTable = computed(() => {
    return !!(props.issuanceType && props.fileType && props.fileCategory);
});
const showPersonModal = ref(false);
const currentEditFileIndex = ref(-1);
const currentEditPermIndex = ref(-1);
interface TreeNode {
    key: string;
    label: string;
    userId?: string;
    nickname?: string;
    orgId?: string;
    orgName?: string;
    disabled?: boolean;
    children?: TreeNode[];
}

const organizationTreeData = ref<TreeNode[]>([]);

const leftSearch = ref('');
const leftChecked = ref<string[]>([]);
const processedTreeData = ref<TreeNode[]>([]);

const rightSearch = ref('');
const rightChecked = ref<string[]>([]);
const selectedList = ref<any[]>([]);
const allPersonnelLeft = computed(() => {
    const result: string[] = [];
    function getAllUserKeys(nodes: TreeNode[]): void {
        nodes.forEach((node) => {
            if (node.userId && !node.disabled) {
                result.push(node.key);
            }
            if (node.children && node.children.length > 0) {
                getAllUserKeys(node.children);
            }
        });
    }
    getAllUserKeys(organizationTreeData.value);
    return result;
});

const checkPersonnelLeft = computed(() => {
    return leftChecked.value.filter((key) => allPersonnelLeft.value.includes(key));
});

const isAllLeftChecked = computed(() => {
    return leftChecked.value.length === allPersonnelLeft.value.length && allPersonnelLeft.value.length > 0;
});

const isAllRightChecked = computed(() => {
    return rightChecked.value.length === selectedList.value.length;
});

const filteredSelectedList = computed(() => {
    if (!rightSearch.value) return selectedList.value;
    return selectedList.value.filter((item) => item.label.toLowerCase().includes(rightSearch.value.toLowerCase()));
});
watch(
    itemList,
    (newList, oldList) => {
        if (oldList && oldList.length > 0) {
            newList.forEach((file, fileIndex) => {
                file.permissions.forEach((perm, permIndex) => {
                    const oldFile = oldList?.[fileIndex];
                    const oldPerm = oldFile?.permissions?.[permIndex];
                    if (oldPerm && oldFile.fileName && file.fileName !== oldFile.fileName) {
                        perm.receivedBy = [];
                    }
                });
            });
        }
    },
    { deep: true }
);

const allPersonOptions = ref<any[]>([]);
async function getPersonOptions(params: { fileId: string; fileForm: number; filePermission: number }) {
    const res = await $apis.nebula.api.v1.issuanceApplication.getUserList(params);

    function getAllUsersFromTree(nodes: TreeNode[]): any[] {
        const result: any[] = [];

        nodes.forEach((node) => {
            if (node.userId) {
                result.push({
                    key: node.key,
                    label: node.label,
                    userId: node.userId,
                    nickname: node.nickname,
                    orgName: node.orgName,
                    disabled: node.disabled
                });
            }

            if (node.children && node.children.length > 0) {
                result.push(...getAllUsersFromTree(node.children));
            }
        });

        return result;
    }

    function buildTreeData(orgData: any): TreeNode[] {
        const result: TreeNode[] = [];
        const children: TreeNode[] = [];
        if (orgData.userInfo && Array.isArray(orgData.userInfo)) {
            orgData.userInfo.forEach((user: any) => {
                children.push({
                    key: user.userId,
                    label: user.nickname,
                    userId: user.userId,
                    nickname: user.nickname,
                    orgName: orgData.orgName,
                    disabled: user.status === 2
                });
            });
        }
        if (orgData.children && Array.isArray(orgData.children)) {
            orgData.children.forEach((child: any) => {
                const childOrgNode = buildTreeData(child)[0];
                children.push(childOrgNode);
            });
        }
        const orgNode: TreeNode = {
            key: orgData.orgId,
            label: orgData.orgName,
            orgId: orgData.orgId,
            orgName: orgData.orgName
        };
        if (children.length > 0) {
            orgNode.children = children;
        }

        result.push(orgNode);
        return result;
    }

    if (res.data?.organizationUserInfo) {
        organizationTreeData.value = buildTreeData(res.data.organizationUserInfo);
        processedTreeData.value = organizationTreeData.value;
        allPersonOptions.value = getAllUsersFromTree(organizationTreeData.value);

        // 设置左侧树中已选择用户的选中状态
        const selectedUserIds = selectedList.value.map((item) => item.userId);
        leftChecked.value = selectedUserIds.filter((userId) =>
            allPersonOptions.value.some((option) => option.userId === userId)
        );
    } else {
        organizationTreeData.value = [];
        processedTreeData.value = [];
        allPersonOptions.value = [];
        leftChecked.value = [];
    }
}

function openPersonModal(row: any) {
    if (!row.fileName) {
        window.$message.warning('请先选择文件名称');
        return;
    }
    if (!row.fileForm) {
        window.$message.warning('请先选择文件形式');
        return;
    }
    if (!row.filePermission) {
        window.$message.warning('请先选择文件权限');
        return;
    }

    currentEditFileIndex.value = row.fileIndex;
    currentEditPermIndex.value = row.permIndex;
    const current = row.receivedBy || [];

    selectedList.value = current.map((p: any) => ({
        key: p.userId || p,
        label: p.userName || p.userNickname || p.name || p,
        userId: p.userId || p,
        nickname: p.userName || p.userNickname || p.name || p
    }));

    rightChecked.value = selectedList.value.map((item) => item.key);

    const params = {
        fileId: row.fileId,
        fileForm: row.fileForm,
        filePermission: row.filePermission
    };
    getPersonOptions(params);
    showPersonModal.value = true;
}

function updateLeftChecked(keys: string[]) {
    leftChecked.value = keys;
}

function toggleLeftCheckAll(checked: boolean) {
    leftChecked.value = checked ? allPersonnelLeft.value : [];
}
function toggleRightCheckAll(checked: boolean) {
    rightChecked.value = checked ? selectedList.value.map((item) => item.key) : [];
}

function handleAdd() {
    const newPersons: any[] = [];

    function getSelectedPersons(nodes: TreeNode[]): void {
        nodes.forEach((node) => {
            if (node.userId && !node.disabled && checkPersonnelLeft.value.includes(node.key)) {
                // 检查是否已经存在该用户
                const existingUser = selectedList.value.find((item) => item.userId === node.userId);
                if (!existingUser) {
                    newPersons.push({
                        key: node.key,
                        label: node.label,
                        userId: node.userId,
                        nickname: node.nickname,
                        orgName: node.orgName
                    });
                }
            }
            if (node.children && node.children.length > 0) {
                getSelectedPersons(node.children);
            }
        });
    }

    getSelectedPersons(organizationTreeData.value);

    selectedList.value.push(...newPersons);
    leftChecked.value = [];
}

function handleRemove() {
    selectedList.value = selectedList.value.filter((item) => !rightChecked.value.includes(item.key));
    rightChecked.value = [];
}

function confirmPersonSelection() {
    if (currentEditFileIndex.value >= 0 && currentEditPermIndex.value >= 0) {
        const selectedPersons: PersonItem[] = selectedList.value.map((item) => ({
            userId: item.userId,
            userName: item.label
        }));
        itemList.value[currentEditFileIndex.value].permissions[currentEditPermIndex.value].receivedBy = selectedPersons;
        update();
    }
    showPersonModal.value = false;
}

const store = useStore();
const fileList = ref<any[]>([]);
const fileListLoading = ref(false);

function getOrgTypeForExternal() {
    return store.userInfo.organizationType === 0 ? 1 : 2;
}

async function fetchFileList() {
    fileListLoading.value = true;
    try {
        let list = [];
        if (props.fileType === 1) {
            const res = await $apis.nebula.api.v1.internal.list({
                docCategoryIds: props.fileCategory ? [props.fileCategory] : [],
                status: 3,
                page: 1,
                pageSize: 99999
            });
            list = res.data?.data ?? [];
        } else if (props.fileType === 2) {
            const orgType = getOrgTypeForExternal();
            const res = await $apis.nebula.api.v1.external.getList({
                typeDictionaryNodeIds: props.fileCategory ? [props.fileCategory] : [],
                status: 3,
                orgType,
                page: 1,
                pageSize: 99999
            });
            list = res.data?.data ?? [];
        }
        fileList.value = Array.isArray(list) ? list : [];
    } finally {
        fileListLoading.value = false;
    }
}

watch(
    () => props.fileCategory,
    () => {
        fetchFileList();
    },
    { immediate: true }
);

function getFileNameOptions() {
    return fileList.value.map((file) => ({
        label: file.name,
        value: file.name,
        number: file.no || file.number || file.No,
        version: file.versionNo || file.version
    }));
}
function getFileNoOptions() {
    return fileList.value.map((file) => ({
        label: file.no || file.number || file.No,
        value: file.no || file.number || file.No,
        fileName: file.name,
        version: file.versionNo || file.version
    }));
}
function onFileNameSelect(row: any, selectedValue: string) {
    const selectedFile = fileList.value.find((file) => file.name === selectedValue);
    if (selectedFile) {
        row.number = selectedFile.no || selectedFile.number || selectedFile.No;
        row.version = selectedFile.versionNo || selectedFile.version;
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = selectedFile.name;
            itemList.value[fileIndex].number = selectedFile.no || selectedFile.number || selectedFile.No;
            itemList.value[fileIndex].version = selectedFile.versionNo || selectedFile.version;
            itemList.value[fileIndex].fileId = selectedFile.id;
            update();
        }
    } else {
        row.fileNo = '';
        row.version = '';
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].number = '';
            itemList.value[fileIndex].version = '';
            itemList.value[fileIndex].fileId = '';
            update();
        }
    }
}
function onFileNoSelect(row: any, selectedValue: string) {
    const selectedFile = fileList.value.find((file) => (file.no || file.number || file.No) === selectedValue);
    if (selectedFile) {
        row.fileName = selectedFile.name;
        row.version = selectedFile.versionNo || selectedFile.version;
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = selectedFile.name;
            itemList.value[fileIndex].number = selectedFile.no || selectedFile.number || selectedFile.No;
            itemList.value[fileIndex].version = selectedFile.versionNo || selectedFile.version;
            itemList.value[fileIndex].fileId = selectedFile.id;
            update();
        }
    } else {
        row.fileName = '';
        row.version = '';
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = '';
            itemList.value[fileIndex].version = '';
            itemList.value[fileIndex].fileId = '';
            update();
        }
    }
}

function receivedByFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((p: any) => p.userName || p.name || p.userNickname || p.id).join('，');
    }
    return '选择接收人';
}

function getFileFormOptions(row: any) {
    const issuanceType = row.issuanceType || props.issuanceType;
    if (issuanceType === 1) {
        return [
            { label: '电子文件', value: 1 },
            { label: '纸质文件', value: 2 }
        ];
    } else if (issuanceType === 2) {
        return [{ label: '电子文件', value: 1 }];
    }
    return [];
}

function getFilePermissionOptions(row: any) {
    const issuanceType = row.issuanceType || props.issuanceType;
    const fileForm = row.fileForm;
    if (issuanceType === 1 && fileForm === 1) {
        return [
            { label: '查阅', value: 1 },
            { label: '查阅/下载', value: 2 }
        ];
    } else if (issuanceType === 1 && fileForm === 2) {
        return [{ label: '一次下载', value: 3 }];
    } else if (issuanceType === 2 && fileForm === 1) {
        return [{ label: '一次下载', value: 3 }];
    }
    return [];
}
watch(
    itemList,
    (newList) => {
        newList.forEach((file) => {
            file.permissions.forEach((perm) => {
                const formOpts = getFileFormOptions({ ...perm, issuanceType: props.issuanceType });
                if (!formOpts.some((opt) => String(opt.value) === String(perm.fileForm))) {
                    perm.fileForm = '';
                    perm.filePermission = '';
                }
                const permOpts = getFilePermissionOptions({ ...perm, issuanceType: props.issuanceType });
                if (!permOpts.some((opt) => String(opt.value) === String(perm.filePermission))) {
                    perm.filePermission = '';
                }
            });
        });
    },
    { deep: true }
);

function fileFormFormatter({ cellValue }: { cellValue: number }) {
    if (!cellValue) return '';
    const option = fileFormOptions.find((opt) => opt.value === cellValue);
    return option ? option.label : cellValue;
}
function filePermissionFormatter({ cellValue }: { cellValue: number }) {
    if (cellValue === 1) return '查阅';
    if (cellValue === 2) return '查阅/下载';
    if (cellValue === 3) return '一次下载';
    return cellValue;
}
const tooltipConfig = {
    contentMethod({ column, row }: { column: any; row: any }) {
        if (column.field === 'receivedBy') {
            if (Array.isArray(row.receivedBy) && row.receivedBy.length > 0) {
                return row.receivedBy.map((p: any) => p.userName || p.name || p.userNickname || p.id).join('，');
            }
            return '未选择接收人';
        }
    }
};

defineExpose({
    tableValid,
    receivedByFormatter,
    tooltipConfig,
    addFile
});
</script>

<style scoped lang="less">
.table-footer {
    margin-top: 12px;
    text-align: left;
}

.transfer-container {
    display: flex;
    gap: 8px;
    height: 400px;
    .transfer-panel {
        width: 360px;
        border: 1px solid #e0e0e6;
        padding: 12px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .panel-title {
            font-weight: bold;
        }
        .panel-content {
            flex: 1;
            overflow-y: auto;
            &::-webkit-scrollbar {
                width: 2px;
            }
            &::-webkit-scrollbar-thumb {
                background-color: #ccc;
            }
        }
        .checkbox-item {
            margin-bottom: 4px;
        }
    }
    .transfer-action {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
    }
}
</style>

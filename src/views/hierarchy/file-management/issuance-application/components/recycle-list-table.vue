<template>
    <div class="recycle-list-vxe-table">
        <n-data-table
            :columns="columns"
            :data="tableData"
            :row-key="(row) => row.fileId"
            :checked-row-keys="checkedRowKeys"
            @update:checked-row-keys="handleSelectionChange"
            :scroll-x="1600"
            :loading="loading"
        >
            <template #a="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 1)"
                        @update:value="(val) => handleABCChange(row, 'a', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.a" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isRecycle"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #b="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 2)"
                        @update:value="(val) => handleABCChange(row, 'b', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.b" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isRecycle"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #c="{ row }">
                <n-scrollbar class="w-100% max-h-80px">
                    <n-checkbox-group
                        :value="getCheckedUsers(row.fileId, 3)"
                        @update:value="(val) => handleABCChange(row, 'c', val)"
                    >
                        <n-grid :cols="12" :y-gap="5" :x-gap="5">
                            <n-gi v-for="item in row.c" :key="item.value" :span="4">
                                <n-checkbox
                                    :value="item.value"
                                    :label="item.label"
                                    size="small"
                                    :disabled="item.isRecycle"
                                />
                            </n-gi>
                        </n-grid>
                    </n-checkbox-group>
                </n-scrollbar>
            </template>
            <template #todo="{ row }">
                <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
interface TableRow {
    id: string;
    fileId: string;
    fileName: string;
    number: string;
    version: string;
    permissions: Array<{
        fileForm: number;
        filePermission: number;
        receivedBy: Array<{
            userId: string;
            nickname: string;
            status: number;
        }>;
    }>;
    // 为了兼容现有的模板，添加这些字段
    fileNo?: string;
    fileForm?: string;
    a: Array<{ label: string; value: string; isRecycle?: boolean }>;
    b: Array<{ label: string; value: string; isRecycle?: boolean }>;
    c: Array<{ label: string; value: string; isRecycle?: boolean }>;
}

interface BackDataItem {
    inventoryId: string;
    permissions: Array<{
        filePermission: number;
        receivedBy: string[];
    }>;
}

const props = defineProps<{ modelValue: BackDataItem[]; id: string }>();
const emit = defineEmits(['update:modelValue']);

const columns = ref<any[]>([
    { type: 'selection', width: 50, fixed: 'left' },
    { title: '序号', key: 'key', align: 'center', width: 60, render: (_: any, index: number) => `${index + 1}` },
    { key: 'fileName', title: '文件名称', align: 'center', fixed: 'left', minWidth: 160, ellipsis: { tooltip: true } },
    { key: 'number', title: '文件编号', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'version', title: '版本/版次', align: 'center', width: 100, ellipsis: { tooltip: true } },
    { key: 'a', title: '内发:电子文件-查阅', align: 'center', ellipsis: { tooltip: true } },
    { key: 'b', title: '内发:电子文件-查阅/下载', align: 'center', ellipsis: { tooltip: true } },
    { key: 'c', title: '内发:纸质文件-一次下载', align: 'center', ellipsis: { tooltip: true } },
    { key: 'todo', title: '操作', align: 'center', fixed: 'right', width: 100 }
]);

const tableData = ref<TableRow[]>([]);
const checkedMap = ref<
    Record<
        string,
        Array<{
            filePermission: number;
            receivedBy: string[];
        }>
    >
>({});
const checkedRowKeys = ref<string[]>([]);
const loading = ref(false);

const loadData = async () => {
    loading.value = true;
    try {
        // 通过id获取数据
        const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.id);
        console.log('API响应:', res);

        // 根据实际返回的数据结构获取数据
        let rawData = [];
        if (res && res.data && res.data.data && Array.isArray(res.data.data)) {
            rawData = res.data.data;
        } else if (res && res.data && Array.isArray(res.data)) {
            rawData = res.data;
        } else {
            console.warn('API返回的数据格式不正确:', res);
            rawData = [];
        }

        // 处理数据，将 permissions 转换为 a, b, c 格式
        tableData.value = rawData.map((item: any) => {
            const processedItem: TableRow = {
                ...item,
                a: [],
                b: [],
                c: []
            };

            if (item.permissions && Array.isArray(item.permissions)) {
                item.permissions.forEach((permission: any) => {
                    const userList = permission.receivedBy.map((user: any) => ({
                        label: user.nickname,
                        value: user.userId,
                        isRecycle: user.status === 3 // status 3 表示已回收
                    }));

                    if (permission.filePermission === 1) {
                        processedItem.a = userList;
                    } else if (permission.filePermission === 2) {
                        processedItem.b = userList;
                    } else if (permission.filePermission === 3) {
                        processedItem.c = userList;
                    }
                });
            }

            return processedItem;
        });

        initCheckedMap();
    } catch (error) {
        console.error('获取数据失败:', error);
        window.$message.error('获取数据失败');
        tableData.value = [];
    } finally {
        loading.value = false;
    }
};

function initCheckedMap() {
    checkedMap.value = {};
    for (const row of tableData.value) {
        checkedMap.value[row.fileId] = [];
    }
    // 回填 modelValue
    for (const item of props.modelValue || []) {
        if (checkedMap.value[item.inventoryId]) {
            checkedMap.value[item.inventoryId] = [...item.permissions];
        }
    }
    updateCheckedRowKeys();
}

function updateCheckedRowKeys() {
    checkedRowKeys.value = tableData.value
        .filter((row) => {
            const checked = checkedMap.value[row.fileId];
            return checked && checked.length > 0;
        })
        .map((row) => row.fileId);
}

function handleABCChange(row: TableRow, key: 'a' | 'b' | 'c', val: (string | number)[]) {
    const filePermission = key === 'a' ? 1 : key === 'b' ? 2 : 3;
    const existingIndex = checkedMap.value[row.fileId].findIndex((p) => p.filePermission === filePermission);

    if (val.length > 0) {
        const permission = {
            filePermission,
            receivedBy: val.map(String)
        };

        if (existingIndex >= 0) {
            checkedMap.value[row.fileId][existingIndex] = permission;
        } else {
            checkedMap.value[row.fileId].push(permission);
        }
    } else if (existingIndex >= 0) {
        checkedMap.value[row.fileId].splice(existingIndex, 1);
    }

    updateCheckedRowKeys();
    emitBackData();
}

function handleSelectionChange(keys: (string | number)[]) {
    const stringKeys = keys.map(String);
    checkedRowKeys.value = stringKeys;

    for (const row of tableData.value) {
        const isChecked = stringKeys.includes(row.fileId);
        const wasChecked = checkedMap.value[row.fileId].length > 0;

        if (isChecked && !wasChecked) {
            // 选中：全选 a/b/c（过滤掉 isRecycle 为 true 的项）
            const permissions = [];

            const aUsers = row.a.filter((i) => !i.isRecycle).map((i) => i.value);
            if (aUsers.length > 0) {
                permissions.push({ filePermission: 1, receivedBy: aUsers });
            }

            const bUsers = row.b.filter((i) => !i.isRecycle).map((i) => i.value);
            if (bUsers.length > 0) {
                permissions.push({ filePermission: 2, receivedBy: bUsers });
            }

            const cUsers = row.c.filter((i) => !i.isRecycle).map((i) => i.value);
            if (cUsers.length > 0) {
                permissions.push({ filePermission: 3, receivedBy: cUsers });
            }

            checkedMap.value[row.fileId] = permissions;
        } else if (!isChecked && wasChecked) {
            // 取消：清空
            checkedMap.value[row.fileId] = [];
        }
    }
    emitBackData();
}

function getCheckedUsers(fileId: string, filePermission: number): string[] {
    const permissions = checkedMap.value[fileId] || [];
    const permission = permissions.find((p) => p.filePermission === filePermission);
    return permission ? permission.receivedBy : [];
}

function emitBackData() {
    const backData = Object.entries(checkedMap.value)
        .filter(([, permissions]) => permissions.length > 0)
        .map(([inventoryId, permissions]) => {
            // 找到对应的表格行数据
            const rowData = tableData.value.find((row) => row.fileId === inventoryId);

            // 转换权限数据，包含人员名字
            const permissionsWithNames = permissions.map((permission) => {
                const filePermission = permission.filePermission;
                let userList: Array<{ label: string; value: string; isRecycle?: boolean }> = [];

                // 根据权限类型获取对应的用户列表
                if (filePermission === 1) {
                    userList = rowData?.a || [];
                } else if (filePermission === 2) {
                    userList = rowData?.b || [];
                } else if (filePermission === 3) {
                    userList = rowData?.c || [];
                }

                // 过滤出选中的用户，并转换为包含名字的格式
                const selectedUsers = userList
                    .filter((user) => permission.receivedBy.includes(user.value))
                    .map((user) => ({
                        userId: user.value,
                        nickname: user.label
                    }));

                return {
                    filePermission,
                    receivedBy: permission.receivedBy,
                    receivedByNames: selectedUsers
                };
            });

            return {
                inventoryId,
                permissions: permissionsWithNames,
                // 添加文件信息
                fileInfo: {
                    fileName: rowData?.fileName || '',
                    number: rowData?.number || '',
                    version: rowData?.version || ''
                }
            };
        });
    emit('update:modelValue', backData);
}

onMounted(() => {
    loadData();
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('../models/recycle-record.vue'),
        width: '60%',
        props: { id: row.id }
    });
};
</script>

<style scoped lang="less"></style>

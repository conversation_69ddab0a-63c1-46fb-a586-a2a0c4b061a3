<template>
    <alert-content :showDefaultButtons="false">
        <n-descriptions label-placement="left" :column="2" size="small" class="mb-2">
            <n-descriptions-item label="文件名称">{{ data.fileName }}</n-descriptions-item>
            <n-descriptions-item label="文件编号">{{ data.fileNo }}</n-descriptions-item>
        </n-descriptions>
        <p>回收记录</p>
        <n-search-table-page
            :data-table-props="{
                columns,
                data: data.recycleRecords,
                size: 'small',
                bordered: true,
                pagination: false,
                scrollX: 1800
            }"
            :search-props="{ show: false }"
            :table-props="{ showPagination: false }"
        />
    </alert-content>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, h } from 'vue';
import AlertContent from '@/components/alert-content.vue';

const props = defineProps<{ id: string }>();

const data = ref<any>({});

const MAX_DISPLAY = 4;

// 为每个单元格维护展开状态
const expandedStates = ref<Record<string, boolean>>({});

const renderHandover = (val: string | string[], rowIndex: number, columnKey: string) => {
    const list = Array.isArray(val) ? val : (val || '').split(',').filter(Boolean);
    if (list.length <= MAX_DISPLAY) {
        return list.join('、');
    }

    const stateKey = `${rowIndex}-${columnKey}`;
    const isExpanded = expandedStates.value[stateKey] || false;

    const displayList = isExpanded ? list : list.slice(0, MAX_DISPLAY);
    const hasMore = list.length > MAX_DISPLAY;

    const elements: any[] = [];
    displayList.forEach((person: string, index: number) => {
        elements.push(h('span', person));
        if (index < displayList.length - 1) {
            elements.push(h('span', '、'));
        }
    });

    if (hasMore) {
        elements.push(
            h('span', '、'),
            h(
                'span',
                {
                    style: { color: '#2080f0', cursor: 'pointer', fontSize: '12px' },
                    onClick: () => {
                        expandedStates.value[stateKey] = !isExpanded;
                    }
                },
                isExpanded ? '收起' : `+${list.length - MAX_DISPLAY}人`
            )
        );
    }

    return h('div', { style: 'line-height: 1.5;' }, elements);
};

const columns = [
    { title: '序号', key: 'index', width: 60, render: (_: any, idx: number) => idx + 1 },
    { title: '回收发起人', key: 'initiator', width: 100 },
    { title: '回收原因', key: 'reason', width: 120 },
    {
        title: '交还人-内发：电子文件-查询',
        key: 'handoverQuery',
        width: 220,
        render: (row: any, index: number) => renderHandover(row.handoverQuery, index, 'handoverQuery')
    },
    {
        title: '交还人-内发：电子文件-查询/下载',
        key: 'handoverDownload',
        width: 300,
        render: (row: any, index: number) => renderHandover(row.handoverDownload, index, 'handoverDownload')
    },
    {
        title: '交还人-内发：纸质文件-一次下载',
        key: 'handoverPaper',
        width: 280,
        render: (row: any, index: number) => renderHandover(row.handoverPaper, index, 'handoverPaper')
    },
    { title: '审批人', key: 'approver', width: 100 },
    { title: '批准人', key: 'authorizer', width: 100 },
    { title: '回收日期', key: 'recycleDate', width: 100 }
];

onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeRecycleInfo(props.id);
    data.value = res.data;
});
</script>

<style scoped>
.mb-2 {
    margin-bottom: 16px;
}
</style>

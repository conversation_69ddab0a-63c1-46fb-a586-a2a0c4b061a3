<template>
    <alert-content :showDefaultButtons="false">
        <n-space vertical size="large">
            <template #header>
                <n-space align="center" justify="space-between" style="width: 100%">
                    <span style="font-size: 18px; font-weight: bold">纸质文件处置详情</span>
                    <n-tag :type="statusTagType">{{ statusText }}</n-tag>
                </n-space>
            </template>
            <n-descriptions :column="2" size="large" label-placement="left">
                <n-descriptions-item label="发放人">{{ issuer }}</n-descriptions-item>
                <n-descriptions-item label="发放日期">{{ issueDate }}</n-descriptions-item>
                <n-descriptions-item label="发放类型">{{ issuanceType }}</n-descriptions-item>
                <n-descriptions-item label="文件类型">{{ fileType }}</n-descriptions-item>
                <n-descriptions-item label="文件类别">{{ fileCategory }}</n-descriptions-item>
                <n-descriptions-item label="发放原因">{{ reason }}</n-descriptions-item>
                <n-descriptions-item label="希望发放日期">{{ wishDate }}</n-descriptions-item>
            </n-descriptions>
            <n-search-table-page
                :data-table-props="{
                    columns,
                    data: paperList,
                    size: 'small',
                    bordered: true,
                    pagination: false,
                    scrollX: 1200
                }"
                :search-props="{ show: false }"
                :table-props="{ showPagination: false }"
            >
                <template #table_action="{ row }">
                    <n-button @click="showDisposalRecord(row)" size="tiny" type="primary">处置记录</n-button>
                </template>
            </n-search-table-page>
        </n-space>
    </alert-content>
</template>

<script setup lang="ts">
import { computed, h } from 'vue';
import { NTag, NTooltip } from 'naive-ui';
import AlertContent from '@/components/alert-content.vue';

const props = defineProps({ row: { type: Object, default: () => ({}) } });

const status = computed(() => props.row.status || '');
const issuer = computed(() => props.row.applicant || '');
const issueDate = computed(() => props.row.applyDate || '');
const issuanceType = computed(() => props.row.issuanceType || '');
const fileType = computed(() => props.row.fileType || '');
const fileCategory = computed(() => props.row.category || '');
const reason = computed(() => props.row.reason || '');
const wishDate = computed(() => props.row.expectedIssueDate || '');
const itemList = computed(() => props.row.itemList || []);

// 只保留纸质文件
const paperList = computed(() => {
    return itemList.value
        .map((item: any) => {
            const paperPerm = (item.permissions || []).find((p: any) => p.fileForm === '纸质文件');
            if (!paperPerm) return null;
            return {
                ...item,
                ...paperPerm,
                issueCount: props.row.issueCount,
                signCount: props.row.signCount,
                disposalCount: props.row.disposalCount
            };
        })
        .filter(Boolean);
});

const statusTagType = computed(() => {
    if (status.value === '全部处置') return 'success';
    if (status.value === '部分处置') return 'warning';
    return 'error';
});
const statusText = computed(() => status.value || '均未处置');

const columns = [
    { title: '序号', key: 'index', width: 60, render: (_: any, idx: number) => idx + 1 },
    { title: '文件名称', key: 'fileName', width: 120 },
    { title: '文件编号', key: 'fileNo', width: 120 },
    { title: '版本/版次', key: 'version', width: 100 },
    { title: '文件形式', key: 'fileForm', width: 100 },
    { title: '发放份数', key: 'issueCount', width: 100 },
    { title: '回收份数', key: 'signCount', width: 100 },
    { title: '处置份数', key: 'disposalCount', width: 100 },
    {
        title: '交还人',
        key: 'personList',
        width: 200,
        render: (row: any) => renderPersons(row.personList)
    },
    { title: '操作', key: 'action', width: 100, align: 'center' as const }
];

function renderPersons(personList: any[]) {
    if (!personList || !personList.length) return '';
    return personList
        .map((person: any) => {
            if (person.status === '已回收') {
                return h(
                    NTooltip,
                    { trigger: 'hover' },
                    {
                        trigger: () =>
                            h(
                                'span',
                                { style: { color: '#2080f0', textDecoration: 'underline', cursor: 'pointer' } },
                                person.name
                            ),
                        default: () => `处置日期: ${person.recycleDate}`
                    }
                );
            }
            return h('span', person.name);
        })
        .reduce(
            (acc: any[], cur: any, idx: number, arr: any[]) =>
                acc.concat(cur, idx < arr.length - 1 ? h('span', '、') : []),
            []
        );
}

function showDisposalRecord(row: any) {
    $alert.dialog({
        title: '处置记录',
        content: import('./disposal-record.vue'),
        width: '60%',
        props: { id: row.id }
    });
}
</script>

<style scoped></style>

<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            class="mt-2"
            ref="formRef"
            label-align="right"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="处置人:" :span="12">
                    <n-input disabled :value="userInfo.name" />
                </n-form-item-gi>
                <n-form-item-gi label="处置日期" :span="12">
                    <n-input disabled :value="today" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-input disabled :value="row.issuanceType" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-input disabled :value="row.fileType" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input disabled :value="row.category" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12">
                    <n-input disabled :value="row.reason" />
                </n-form-item-gi>
                <n-form-item-gi label="处置方式" path="disposalMethod" :span="12">
                    <n-input
                        v-model:value="formData.disposalMethod"
                        maxlength="50"
                        placeholder="请输入处置方式"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi path="exchangePerson" :span="24">
                    <div class="flex-v w-100%">
                        <span class="my-10px text-14px required-field">纸质文件处置清单</span>
                        <paper-dispose-list-table v-model="formData.exchangePerson" :id="row.id" />
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormInst, FormRules } from 'naive-ui';
import AlertContent from '@/components/alert-content.vue';
import PaperDisposeListTable from '../components/paper-dispose-list-table.vue';

const { row } = defineProps<{ row: any }>();
const userInfo = { name: '当前用户' };
const today = new Date().toISOString().slice(0, 10);

const formRef = ref<FormInst>();

const formData = reactive({
    disposalMethod: '',
    exchangePerson: []
});

const rules = computed<FormRules>(() => ({
    disposalMethod: [{ required: true, message: '请输入处置方式', trigger: ['blur', 'input'] }],
    exchangePerson: [
        {
            required: true,
            trigger: 'blur',
            validator: () => {
                // 只要有一个选中的即可
                if (!formData.exchangePerson.length) {
                    return new Error('请至少选择一个文件的人员进行处置');
                }
                return true;
            }
        }
    ]
}));

const onSubmit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            await new Promise((resolve) => {
                window.$dialog.info({
                    title: '确认提示',
                    content: '确认后将发起审批流程，是否确认？',
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                        window.$message.success('处置流程已发起');
                        resolve(true);
                    }
                });
            });
        })
        .catch((errors) => {
            if (errors && errors[0] && errors[0][0]) {
                window.$message.error(errors[0][0].message);
            } else {
                window.$message.error('请完善表单信息');
            }
            return Promise.reject();
        });
};
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>

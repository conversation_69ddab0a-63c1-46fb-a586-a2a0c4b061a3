<template>
    <alert-content :on-default-save="onSubmit" :buttons="buttons">
        <n-form
            class="mt-2"
            ref="formRef"
            label-align="right"
            :model="params"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="90"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="回收人" :span="12" label-width="100">
                    <n-input disabled :value="name" />
                </n-form-item-gi>
                <n-form-item-gi label="回收日期" :span="12" label-width="100">
                    <n-input disabled :value="today" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12" label-width="100">
                    <n-select disabled :value="row.distributeType" :options="issuanceTypeOptions" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12" label-width="100">
                    <n-select disabled :value="row.fileType" :options="fileTypeOptions" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12" label-width="100">
                    <n-input disabled :value="row.fileCategory" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12" label-width="100">
                    <n-input disabled :value="row.reason" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" label-width="100">
                    <n-input disabled :value="row.otherReason" />
                </n-form-item-gi>
                <n-form-item-gi label="期望发放日期" :span="12" label-width="100">
                    <n-input disabled :value="dayjs(row.wishDistributeDate).format('YYYY-MM-DD')" />
                </n-form-item-gi>
                <n-form-item-gi label="回收原因" path="reason" :span="12" label-width="100">
                    <n-select
                        v-model:value="params.reason"
                        :options="reasonOptions"
                        placeholder="请选择回收原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi
                    v-if="params.reason === '其他'"
                    label="其他原因"
                    path="otherReason"
                    :span="24"
                    label-width="100"
                >
                    <n-input
                        v-model:value="params.otherReason"
                        maxlength="50"
                        placeholder="请输入其他原因"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="checked">
                    <div class="flex-v w-100%">
                        <span class="ml-18px mb-10px text-14px required-field">发放清单</span>
                        <recycle-list-vxe-table ref="recycleListVxeTableRef" :id="row.id" v-model="params.checked" />
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormInst, FormRules } from 'naive-ui';
import AlertContent, { ButtonsConfig } from '@/components/alert-content.vue';
import RecycleListVxeTable from '../components/recycle-list-table.vue';
import dayjs from 'dayjs';
import useStore from '@/store/modules/main';

const buttons: ButtonsConfig = {
    save: {
        text: '回收'
    }
};
const store = useStore();
const { row } = defineProps<{ row: any }>();
const name = store.userInfo.nickname;

const today = dayjs().format('YYYY-MM-DD');

const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

// 原因选项
const reasonOptions = [
    { label: '员工离职/调离', value: '员工离职/调离' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '文件正式作废', value: '文件正式作废' },
    { label: '文件过期失效', value: '文件过期失效' },
    { label: '错误发放纠正', value: '错误发放纠正' },
    { label: '定期清理', value: '定期清理' },
    { label: '其他', value: '其他' }
];

// 表单数据
const params = reactive({
    reason: null,
    otherReason: null,
    checked: []
});

const formRef = ref<FormInst>();

watch(
    () => params.reason,
    (newReason) => {
        if (newReason !== '其他') {
            params.otherReason = null;
        }
        nextTick(() => {
            formRef.value?.validate();
        });
    }
);

// 表单验证规则
const rules = computed<FormRules>(() => ({
    reason: [{ required: true, message: '请选择回收原因', trigger: ['blur', 'change'] }],
    otherReason: [
        {
            required: params.reason === '其他',
            message: '请输入其他原因',
            trigger: ['blur', 'change']
        }
    ],
    checked: [
        {
            required: true,
            trigger: ['blur', 'change'],
            validator: () => {
                if (!params.checked.length) {
                    return new Error('请至少选择一个文件的人员进行回收');
                }
                return true;
            }
        }
    ]
}));

// 提交表单
const onSubmit = async () => {
    try {
        await formRef.value?.validate();
        // 处理回收列表数据，提取人员名字
        const processedRecycleList = params.checked.map((item: any) => {
            // 将权限数据转换为表格需要的格式
            const a: Array<{ label: string; value: string }> = [];
            const b: Array<{ label: string; value: string }> = [];
            const c: Array<{ label: string; value: string }> = [];

            item.permissions.forEach((permission: any) => {
                const userNames = permission.receivedByNames || permission.receivedBy || [];
                const displayNames = userNames.map((user: any) =>
                    typeof user === 'string' ? user : user.nickname || user.userId || user
                );

                const userObjects = displayNames.map((name: string) => ({ label: name, value: name }));

                if (permission.filePermission === 1) {
                    a.push(...userObjects);
                } else if (permission.filePermission === 2) {
                    b.push(...userObjects);
                } else if (permission.filePermission === 3) {
                    c.push(...userObjects);
                }
            });

            return {
                fileName: item.fileInfo?.fileName || '',
                fileNo: item.fileInfo?.number || '',
                fileForm: item.fileInfo?.version || '',
                a,
                b,
                c
            };
        });

        const formData = JSON.stringify({
            businessId: 'FILE_RECLAIM',
            version: '1.0.0',
            data: {
                data: {
                    distributeId: row.id,
                    recycleDate: dayjs().valueOf(),
                    recycleReason: params.reason,
                    otherReason: params.reason === '其他' ? params.otherReason : null,
                    recycleList: params.checked
                },
                flowData: {
                    // 回收人信息
                    recycler: {
                        nickname: store.userInfo.nickname,
                        recycleDate: dayjs().format('YYYY-MM-DD')
                    },
                    // 回收原因
                    recycleReason: {
                        reason: params.reason,
                        otherReason: params.reason === '其他' ? params.otherReason : null
                    },
                    // 发放信息
                    distributeData: {
                        distributeTypeText:
                            issuanceTypeOptions.find((opt) => opt.value === row.distributeType)?.label || '',
                        fileTypeText: fileTypeOptions.find((opt) => opt.value === row.fileType)?.label || '',
                        fileCategory: row.fileCategory,
                        reason: row.reason,
                        otherReason: row.otherReason,
                        wishDistributeDateText: dayjs(row.wishDistributeDate).format('YYYY-MM-DD')
                    },
                    // 回收清单（包含人员名字）
                    recycleList: processedRecycleList
                }
            }
        });
        const confirmed = await new Promise<boolean>((resolve) => {
            window.$dialog.info({
                title: '确认提示',
                content: '确认后将发起审批流程，是否确认？',
                positiveText: '确认',
                negativeText: '取消',
                onPositiveClick: () => resolve(true),
                onNegativeClick: () => resolve(false)
            });
        });

        if (!confirmed) return;
        await $hooks.useApprovalProcess('FILE_RECLAIM', formData);
        window.$message.success('回收流程已发起');
        $alert.dialog.close();
    } catch (errors) {
        let errorMessage = '请完善表单信息';

        if (Array.isArray(errors)) {
            // 处理表单验证错误
            const firstError = errors[0];
            if (firstError && firstError[0] && firstError[0].message) {
                errorMessage = firstError[0].message;
            }
        } else if (errors instanceof Error) {
            errorMessage = errors.message;
        } else if (typeof errors === 'string') {
            errorMessage = errors;
        }

        window.$message.error(errorMessage);
        throw errors;
    }
};
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>

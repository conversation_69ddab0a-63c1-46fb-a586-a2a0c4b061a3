<template>
    <alert-content :showDefaultButtons="false">
        <n-space vertical size="large">
            <template #header>
                <n-space align="center" justify="space-between" style="width: 100%">
                    <span style="font-size: 18px; font-weight: bold">发放详情</span>
                    <n-tag :type="statusTagType">{{ statusText }}</n-tag>
                </n-space>
            </template>
            <n-descriptions :column="2" size="large" label-placement="left">
                <n-descriptions-item label="发放人">{{ row.applicant }}</n-descriptions-item>
                <n-descriptions-item label="发放日期">{{
                    dayjs(row.applyDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
                <n-descriptions-item label="发放类型">{{
                    issuanceTypeOptions.find((item) => item.value === row.distributeType)?.label
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类型">{{
                    fileTypeOptions.find((item) => item.value === row.fileType)?.label
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类别">{{ row.fileCategory }}</n-descriptions-item>
                <n-descriptions-item label="发放原因">
                    <n-ellipsis>{{ row.reason }}</n-ellipsis>
                </n-descriptions-item>
                <n-descriptions-item label="其他原因" v-if="row.otherReason">{{ row.otherReason }}</n-descriptions-item>
                <n-descriptions-item label="希望发放日期" v-if="row.wishDistributeDate">{{
                    dayjs(row.wishDistributeDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
            </n-descriptions>

            <n-search-table-page
                :data-table-props="{
                    columns,
                    data: row.itemList,
                    size: 'small',
                    bordered: true,
                    pagination: false,
                    scrollX: 2000
                }"
                :search-props="{
                    show: false
                }"
                :table-props="{
                    showPagination: false
                }"
            >
                <template #table_action="{ row }">
                    <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
                </template>
            </n-search-table-page>
            <span style="font-weight: bold" class="my-20px">发放审批记录</span>
            <n-space vertical size="small">
                <n-steps size="small" vertical :current="approvalSteps.length" direction="vertical">
                    <n-step
                        v-for="(step, index) in approvalSteps"
                        :key="index"
                        :title="step.title"
                        :description="step.description"
                        :status="step.status"
                    />
                </n-steps>
            </n-space>
        </n-space>
    </alert-content>
</template>

<script setup lang="ts">
import { computed, h, defineComponent, ref } from 'vue';
import { NTag, NTooltip, NSteps, NStep } from 'naive-ui';
import dayjs from 'dayjs';
const props = defineProps({
    row: { type: Object, default: () => ({}) }
});

const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

// 审批步骤数据
const approvalSteps = computed(() => [
    {
        title: '部门主管审批',
        description: '张三 - 2024-01-15 10:30',
        status: 'finish' as const
    },
    {
        title: '技术总监审核',
        description: '李四 - 2024-01-15 14:20',
        status: 'finish' as const
    },
    {
        title: '质量部确认',
        description: '王五 - 2024-01-16 09:15',
        status: 'finish' as const
    },
    {
        title: '总经理批准',
        description: '赵六 - 2024-01-16 16:45（批准人）',
        status: 'finish' as const
    }
]);

const statusTagType = computed(() => {
    if (props.row.status === 1) return 'success';
    if (props.row.status === 2) return 'warning';
    return 'error';
});
const statusText = computed(() => {
    if (props.row.status === 1) return '已发放';
    if (props.row.status === 2) return '部分回收';
    if (props.row.status === 3) return '全部回收';
    return '未知状态';
});

const columns = [
    {
        title: '序号',
        key: 'index',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件名称', key: 'fileName', width: 100, fixed: 'left' as const },
    { title: '文件编号', key: 'fileNo', width: 100 },
    { title: '版本/版次', key: 'version', width: 100 },
    {
        title: '内发：电子文件-查询',
        key: 'nf_edoc_query',
        width: 200,
        render: (row: any) => h(RenderInternalPermission, { row, permissionType: '内发-电子-查询' })
    },
    {
        title: '内发：电子文件-查询/下载',
        key: 'nf_edoc_download',
        width: 200,
        render: (row: any) => h(RenderInternalPermission, { row, permissionType: '内发-电子-查询/下载' })
    },
    {
        title: '内发：纸质文件-一次下载',
        key: 'nf_paper_once',
        width: 200,
        render: (row: any) => h(RenderInternalPermission, { row, permissionType: '内发-纸质-一次下载' })
    },
    {
        title: '外发：电子文件-一次下载',
        key: 'wf_edoc_once',
        width: 200,
        render: (row: any) => h(RenderExternalPermission, { row, permissionType: '外发-电子-一次下载' })
    },
    {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right' as const,
        align: 'center' as const
    }
];

const RenderInternalPermission = defineComponent({
    props: {
        row: { type: Object, required: true },
        permissionType: { type: String, required: true }
    },
    setup(props) {
        const perm = computed(() => {
            return (props.row.permissions || []).find((p: any) => p.type === props.permissionType);
        });

        const showAll = ref(false);
        const maxDisplayCount = 3;

        const renderPerson = (person: any) => {
            if (person.status === '已回收') {
                return h(
                    NTooltip,
                    { trigger: 'hover' },
                    {
                        trigger: () =>
                            h(
                                'span',
                                { style: { color: '#2080f0', textDecoration: 'underline', cursor: 'pointer' } },
                                person.name
                            ),
                        default: () => `回收日期: ${person.recycleDate}`
                    }
                );
            }
            return h('span', person.name);
        };

        return () => {
            if (!perm.value || !perm.value.personList || !perm.value.personList.length) {
                return h('span', '');
            }

            const personList = perm.value.personList;
            const displayList = showAll.value ? personList : personList.slice(0, maxDisplayCount);
            const hasMore = personList.length > maxDisplayCount;

            const elements: any[] = [];

            displayList.forEach((person: any, index: number) => {
                elements.push(renderPerson(person));
                if (index < displayList.length - 1) {
                    elements.push(h('span', '、'));
                }
            });

            if (hasMore) {
                elements.push(
                    h('span', '、'),
                    h(
                        'span',
                        {
                            style: { color: '#2080f0', cursor: 'pointer', fontSize: '12px' },
                            onClick: () => (showAll.value = !showAll.value)
                        },
                        showAll.value ? '收起' : `+${personList.length - maxDisplayCount}人`
                    )
                );
            }

            return h('div', { style: 'line-height: 1.5;' }, elements);
        };
    }
});

// 外发权限渲染组件
const RenderExternalPermission = defineComponent({
    props: {
        row: { type: Object, required: true },
        permissionType: { type: String, required: true }
    },
    setup(props) {
        const perm = computed(() => {
            return (props.row.permissions || []).find((p: any) => p.type === props.permissionType);
        });

        const showAll = ref(false);
        const maxDisplayCount = 3;

        return () => {
            if (!perm.value) return h('span', '');

            const personList = perm.value.personList || [];
            const receiver = perm.value.receiver;

            if (!personList.length && !receiver) {
                return h('span', '');
            }

            const elements: any[] = [];

            if (personList.length > 0) {
                const displayList = showAll.value ? personList : personList.slice(0, maxDisplayCount);
                const hasMore = personList.length > maxDisplayCount;
                displayList.forEach((person: any, index: number) => {
                    elements.push(h('span', person.name));
                    if (index < displayList.length - 1) {
                        elements.push(h('span', '、'));
                    }
                });

                if (hasMore) {
                    elements.push(
                        h('span', '、'),
                        h(
                            'span',
                            {
                                style: { color: '#2080f0', cursor: 'pointer', fontSize: '12px' },
                                onClick: () => (showAll.value = !showAll.value)
                            },
                            showAll.value ? '收起' : `+${personList.length - maxDisplayCount}人`
                        )
                    );
                }
            }
            if (receiver) {
                return h(
                    NTooltip,
                    { trigger: 'hover' },
                    {
                        trigger: () => h('div', { style: 'line-height: 1.5; cursor: pointer;' }, elements),
                        default: () => `接收人: ${receiver}`
                    }
                );
            }

            return h('div', { style: 'line-height: 1.5;' }, elements);
        };
    }
});
onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.row.id);
    console.log(res);
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('./recycle-record.vue'),
        width: '60%',
        props: {
            id: row.id
        }
    });
};
</script>

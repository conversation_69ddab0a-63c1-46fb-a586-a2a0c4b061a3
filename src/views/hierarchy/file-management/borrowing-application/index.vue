<template>
    <div class="borrowing-application">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1600,
                maxHeight: 'calc(100vh - 440px)'
            }"
            :params="params"
            :data-api="
                $apis.test.mockList.bind(null, {
                    'borrowDocumentsCount|1-10': 1,
                    'recoverDocumentsCount|0-10': 1,
                    'borrowTime|1': [1735689600000, 1735776000000, 1735862400000],
                    'dueTime|1': [1736989600000, 1736866000000, 1736772400000],
                    'userNickname|1': ['张三', '李四', '王五', '赵六', '钱七'],
                    'approvalApplyTime|1': ['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04', '2025-01-05'],
                    'approverName|1': ['审核员A', '审核员B', '审核员C', '审核员D'],
                    'reviewerName|1': ['批准人甲', '批准人乙', '批准人丙', '批准人丁'],
                    'recoverNames|1': ['回收员1', '回收员2', '回收员3', '回收员4'],
                    'approvalStatus|1': ['1', '2', '3', '4']
                })
            "
            :search-props="{
                showAdd: store.permissions.indexOf('borrowingApplicationAdd') > -1,
                showInput: false,
                searchInputPlaceholder: '请输入文件编号、名称 / 原文件编号',
                inputWidth: '280px'
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @add="handleEdit(null)"
            @reset="handleReset"
        >
            <template #search_form_middle>
                <n-input class="w-198px" v-model:value="params.no" placeholder="请输入文件编号" />
                <n-input class="w-198px" v-model:value="params.name" placeholder="请输入文件名称" />
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="show = !show">{{ show ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-if="show" :show="show">
                    <n-space>
                        <n-select
                            class="w-178px"
                            v-model:value="params.fileType"
                            :options="fileTypeOptions"
                            clearable
                            placeholder="文件类型"
                        />
                        <select-tree-dictionary
                            class="w-178px!"
                            v-model:value="params.docCategoryIds"
                            :params="params.fileType"
                            placeholder="选择文件类别"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                        />
                        <n-select
                            class="w-128px"
                            v-model:value="params.status"
                            :options="statusOptions"
                            clearable
                            placeholder="状态"
                        />
                        <n-input class="w-178px" v-model:value="params.applicant" placeholder="申请人" />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-permission has="borrowingApplicationExport">
                    <n-button type="warning" @click="handleExport">导出</n-button>
                </n-permission>
            </template>

            <template #table_borrowPeriod="{ row }">
                <n-time :time="row.borrowTime" format="yyyy-MM-dd" />
                至
                <n-time :time="row.dueTime" format="yyyy-MM-dd" />
            </template>
            <template #table_invalidDate="{ row }">
                <n-time :time="row.invalidDate" format="yyyy-MM-dd" />
            </template>
            <template #table_applyDate="{ row }">
                <n-time :time="row.applyDate" format="yyyy-MM-dd" />
            </template>
            <template #table_approvalStatus="{ row }">
                <n-tag
                    :type="$datas.borrowing.status[row.approvalStatus - 1]?.type"
                    round
                    :bordered="false"
                    size="small"
                >
                    {{ $datas.borrowing.status[row.approvalStatus - 1]?.label }}
                </n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center" :wrap="false">
                    <n-permission has="borrowingApplicationDetail">
                        <n-button size="tiny" type="primary" @click="handleDetail(row)">借阅详情</n-button>
                    </n-permission>
                    <n-dropdown
                        v-if="todoOptions.length > 0"
                        trigger="click"
                        :options="todoOptions"
                        @select="(key) => handleTodoMenu(key, row)"
                    >
                        <n-button size="tiny">更多</n-button>
                    </n-dropdown>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';
import useStore from '@/store/modules/main';

const store = useStore();

const searchTablePageRef = ref();
const show = ref(false);
const params = ref<any>({
    no: '', // 文件编号
    name: '', // 文件名称
    originalNo: '', // 原文件编号
    docCategoryIds: [], // 文件类别
    departmentIds: null, // 编制部门
    status: null, // 状态
    hasAttachment: null // 是否有附件
});

// 状态
const statusOptions: any[] = [
    { label: '待提交', value: 1 },
    { label: '待审核', value: 2 },
    { label: '已驳回', value: 3 },
    { label: '已审批', value: 4 }
];

// 文件类型
const fileTypeOptions: any[] = [
    { label: '内部文件', value: 'internal' },
    { label: '外部文件', value: 'external' }
];

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '借阅文件数', key: 'borrowDocumentsCount', align: 'center', fixed: 'left', ellipsis: { tooltip: true } },
    { title: '交还文件数', key: 'recoverDocumentsCount', align: 'center', ellipsis: { tooltip: true } },
    { title: '借阅期限', key: 'borrowPeriod', align: 'center', width: 210, ellipsis: { tooltip: true } },
    { title: '申请人', key: 'userNickname', align: 'center', ellipsis: { tooltip: true } },
    { title: '申请日期', key: 'approvalApplyTime', align: 'center', width: 120 },
    { title: '审核人', key: 'approverName', align: 'center', ellipsis: { tooltip: true } },
    { title: '批准人', key: 'reviewerName', align: 'center', ellipsis: { tooltip: true } },
    { title: '回收人', key: 'recoverNames', align: 'center', ellipsis: { tooltip: true } },
    { title: '状态', key: 'approvalStatus', align: 'center', width: 80 },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 160 }
];

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const handleReset = () => {
    params.value = {
        no: '',
        name: '',
        originalNo: '',
        departmentIds: null,
        status: null,
        hasAttachment: null,
        docCategoryIds: params.value.docCategoryIds || []
    };
    init();
};

const handleExport = () => {
    window.$message.info('点击导出');
};

/**
 * 借阅详情操作
 */
const handleDetail = (row: any) => {
    console.log('详情', row);
};

/**
 * 更多操作
 */
const todoOptions = computed(() => {
    const options = [];
    const perms = store.permissions;
    if (perms.includes('borrowingApplicationEdit')) {
        options.push({ label: '编辑', key: 'edit' });
    }
    if (perms.includes('borrowingApplicationRevoke')) {
        options.push({ label: '撤销', key: 'revoke' });
    }
    if (perms.includes('borrowingApplicationRecycle')) {
        options.push({ label: '回收', key: 'recycle' });
    }
    if (perms.includes('borrowingApplicationDelete')) {
        options.push({ label: '删除', key: 'delete' });
    }
    return options;
});

const handleTodoMenu = (key: string, row: any) => {
    switch (key) {
        case 'edit':
            handleEdit(row);
            break;
        case 'revoke':
            handleRevoke(row);
            break;
        case 'recycle':
            handleRecycle(row);
            break;
        case 'delete':
            handleDelete(row);
            break;
    }
};

/**
 * 新增、编辑
 */
const handleEdit = (row: any) => {
    console.log('编辑', row);
    $alert.dialog({
        title: row ? '编辑' : '新增',
        width: '60%',
        content: import('./models/borrow-form.vue'),
        props: {
            row,
            onSubmit: () => init()
        }
    });
};

/**
 * 撤销操作
 */
const handleRevoke = (row: any) => {
    console.log('撤销', row);
    window.$dialog.warning({
        title: '撤销',
        content: `确认后撤销当前流程，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            window.$message.success(`撤销成功`);
        }
    });
};

/**
 * 回收操作
 */
const handleRecycle = (row: any) => {
    console.log(row);
};

/**
 * 删除操作
 */
const handleDelete = (row: any) => {
    console.log(row);
    window.$dialog.warning({
        title: '删除',
        content: `确认后删除当前流程，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            window.$message.success(`删除成功`);
        }
    });
};
</script>

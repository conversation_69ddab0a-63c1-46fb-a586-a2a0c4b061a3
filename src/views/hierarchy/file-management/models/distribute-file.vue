<template>
    <div class="distribute-file pr-10px">
        <n-grid :cols="2" :x-gap="10" :y-gap="10">
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查询</span>
                <grant-recycle
                    ref="grantRecycleRef0"
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 1
                    }"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(1, persons)"
                    @recycle="(keys: string[]) => onBack(1, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查询/下载</span>
                <grant-recycle
                    ref="grantRecycleRef1"
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 2
                    }"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(2, persons)"
                    @recycle="(keys: string[]) => onBack(2, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：纸质文件 - 一次下载</span>
                <grant-recycle
                    ref="grantRecycleRef2"
                    :params="{
                        fileId: row.id,
                        fileForm: 2,
                        filePermission: 3
                    }"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(3, persons)"
                    @recycle="(keys: string[]) => onBack(3, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">外发：电子文件 - 一次下载</span>
                <grant-recycle
                    ref="grantRecycleRef3"
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 3
                    }"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(4, persons)"
                    @recycle="(keys: string[]) => onBack(4, keys)"
                />
            </n-grid-item>
        </n-grid>
        <div class="flex-v items-start mt-10px">
            <span class="text-14px font-500 mt-20px">发放回收记录</span>
            <n-data-table
                :columns="operateColumns"
                :data="operateData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #in_query="{ row }">
                    <span v-if="row.in_query.length > 0">{{ row.in_query.join('、') }}</span>
                    <span v-else>--</span>
                </template>
                <template #in_query_download="{ row }">
                    <span v-if="row.in_query_download.length > 0">{{ row.in_query_download.join('、') }}</span>
                    <span v-else>--</span>
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 1)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">内发：纸质文件-一次下载变更记录</span>
            <n-data-table
                :columns="changeColumns"
                :data="changeData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #status="{ row }">
                    <n-tag :type="row.status === '已回收' ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.status
                    }}</n-tag>
                </template>
                <template #disposeStatus="{ row }">
                    <n-tag
                        :type="row.disposeStatus === '已处置' ? 'success' : 'error'"
                        size="small"
                        :bordered="false"
                        >{{ row.disposeStatus }}</n-tag
                    >
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 2)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">外发：电子文件-一次下载变更记录</span>
            <n-data-table
                :columns="outDownloadColumns"
                :data="outDownloadData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #isDownload="{ row }">
                    <n-tag :type="row.isDownload ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.isDownload ? '是' : '否'
                    }}</n-tag>
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 2)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">借阅记录</span>
            <n-data-table
                :columns="borrowColumns"
                :data="borrowData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 3)">详情</n-button>
                </template>
            </n-data-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { NTag, PaginationProps } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import useStore from '@/store/modules/main';

const props = withDefaults(
    defineProps<{
        row?: any;
        type?: string;
    }>(),
    {
        row: {},
        type: 'interior'
    }
);
const emit = defineEmits(['update:id']);
const { row } = useVModels(props, emit);
const store = useStore();
const checkTreeData = ref([]);

const grantRecycleRef0 = ref();
const grantRecycleRef1 = ref();
const grantRecycleRef2 = ref();
const grantRecycleRef3 = ref();

const onTransfer = async (type: number, persons: { key: string; label: string }[]) => {
    let fileForm = 1,
        filePermission = 1,
        distributeType = 1;
    if (type === 1) {
        fileForm = 1;
        filePermission = 1;
        distributeType = 1;
    } else if (type === 2) {
        fileForm = 1;
        filePermission = 2;
        distributeType = 1;
    } else if (type === 3) {
        fileForm = 2;
        filePermission = 3;
        distributeType = 1;
    } else if (type === 4) {
        fileForm = 1;
        filePermission = 3;
        distributeType = 2;
    }

    // 兼容各种 row 结构
    const fileId = props.row.id || props.row.fileId || '';
    const fileName = (props.row.fileInfo && props.row.fileInfo.fileName) || props.row.name || props.row.fileName || '';
    const fileNo = props.row.number || props.row.no || props.row.fileNo || '';
    const version = props.row.version || props.row.versionNo || '';
    const typeDictNodeId = props.row.typeDictionaryNodeId || props.row.docCategoryId || '';

    const distributeList = [
        {
            fileId,
            fileName,
            fileNo,
            version,
            permissions: [
                {
                    fileForm,
                    filePermission,
                    receiver: '',
                    receivedBy: persons.map((p) => ({
                        userId: p.key,
                        userName: p.label
                    }))
                }
            ]
        }
    ];

    const result = {
        applicant: store.userInfo.nickname,
        applyDate: Date.now(),
        distributeType,
        fileType: props.type === 'interior' ? 1 : 2,
        typeDictNodeId,
        category: props.row.docCategoryName || props.row.docType,
        reasonDictNodeId: '其他',
        otherReason: props.type === 'interior' ? '内部文件发放' : '外部文件发放',
        wishDistributeDate: null,
        distributeList
    };

    const formData = JSON.stringify({
        businessId: 'FILE_GRANT',
        version: '1.0.0',
        data: { data: result }
    });
    await $hooks.useApprovalProcess('FILE_GRANT', formData);
    window.$message.success('发放申请已提交');
    const targetRef = [grantRecycleRef0.value, grantRecycleRef1.value, grantRecycleRef2.value, grantRecycleRef3.value][
        type - 1
    ];
    if (targetRef && targetRef.getUserTreeData) {
        targetRef.getUserTreeData();
    }
};

const onBack = (type: number, keys: string[]) => {
    switch (type) {
        case 1:
            window.$message.info(`内发：电子文件 - 查询，回收人员：${keys.join(',')}`);
            break;
        case 2:
            window.$message.info(`内发：电子文件 - 查询/下载，回收人员：${keys.join(',')}`);
            break;
        case 3:
            window.$message.info(`内发：纸质文件 - 一次下载，回收人员：${keys.join(',')}`);
            break;
        case 4:
            window.$message.info(`外发：电子文件 - 一次下载，回收人员：${keys.join(',')}`);
            break;
    }
};

/**
 * 表格配置
 */
const pagination = ref<PaginationProps>({
    pageSize: 5,
    prefix: ({ itemCount }) => `共 ${itemCount} 条`
});

/**
 * 发放回收记录
 */
const operateColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'time',
        align: 'center',
        width: 180
    },
    {
        title: '内发：电子文件-查询',
        key: 'in_query',
        align: 'center',
        ellipsis: {
            tooltip: true
        },
        render(row: any) {
            return Array.isArray(row.in_query) ? row.in_query.join('、') : row.in_query;
        }
    },
    {
        title: '内发：电子文件-查询/下载',
        key: 'in_query_download',
        align: 'center',
        ellipsis: {
            tooltip: true
        },
        render(row: any) {
            return Array.isArray(row.in_query_download) ? row.in_query_download.join('、') : row.in_query_download;
        }
    },
    {
        title: '变更人',
        key: 'changer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const operateData = ref([]);
const getOperateData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 20, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            'in_query|1-3': ['@cname'],
            'in_query_download|1-3': ['@cname'],
            'out_download|1-3': ['@cname'],
            changer: '@cname'
        }
    });
    operateData.value = res.data.data;
};

/**
 * 内发：纸质文件-一次下载变更记录
 */
const changeColumns = ref<TableColumns>([
    {
        title: '纸质文件接收人',
        key: 'receiver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '纸质文件状态',
        key: 'status',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件处置状态',
        key: 'disposeStatus',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'issuer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'issueTime',
        align: 'center',
        width: 180
    },
    {
        title: '回收人',
        key: 'recycler',
        align: 'center',
        width: 180
    },
    {
        title: '回收申请时间',
        key: 'recycleTime',
        align: 'center',
        width: 180
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const changeData = ref([]);
const getChangeData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            receiver: '@cname',
            'status|1': ['已回收'],
            'disposeStatus|1': ['已处置', '未处置', '处置中'],
            issuer: '@cname',
            changer: '@cname',
            issueTime: '@date("yyyy-MM-dd HH:mm:ss")',
            recycler: '@cname',
            recycleTime: '@date("yyyy-MM-dd HH:mm:ss")'
        }
    });
    changeData.value = res.data.data;
};

/**
 * 外发：电子文件-一次下载变更记录
 */
const outDownloadColumns = ref<TableColumns>([
    {
        title: '电子文件接收方',
        key: 'receiver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'issuer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'issueTime',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '是否下载',
        key: 'isDownload',
        align: 'center',
        width: 80
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const outDownloadData = ref([]);
const getOutDownloadData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            receiver: '@cname',
            issuer: '@cname',
            issueTime: '@date("yyyy-MM-dd HH:mm:ss")',
            isDownload: '@boolean',
            changer: '@cname'
        }
    });
    outDownloadData.value = res.data.data;
};

/**
 * 借阅记录
 */
const borrowColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'time',
        align: 'center',
        width: 180
    },
    {
        title: '借阅人',
        key: 'borrower',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '借阅期限',
        key: 'borrowPeriod',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const borrowData = ref([]);

const getBorrowData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            borrower: '@cname',
            borrowPeriod: '@date("yyyy-MM-dd") 至 @date("yyyy-MM-dd")',
            changer: '@cname'
        }
    });
    borrowData.value = res.data.data;
};

const handleApproval = (row: any, type: number) => {
    $alert.dialog({
        title: '审批详情',
        content: import('./distribute-file-approval-process.vue'),
        width: '50%',
        props: {
            id: row.id,
            type
        }
    });
};

onMounted(() => {
    getOperateData();
    getChangeData();
    getOutDownloadData();
    getBorrowData();
});
</script>

<style scoped lang="less"></style>
